/** @format */

'use client';

import { colors } from '@/app/colors';
import React, { useState } from 'react';

interface Message {
	id: string;
	text: string;
	isUser: boolean;
	timestamp: Date;
}

const ChatDemo: React.FC = () => {
	const [messages, setMessages] = useState<Message[]>([
		{
			id: '1',
			text: 'Hi! I can help you find amazing places. Try asking me something like "Find cozy cafes near me" or "Show me the best restaurants in Istanbul"',
			isUser: false,
			timestamp: new Date(),
		},
	]);
	const [inputText, setInputText] = useState('');
	const [isTyping, setIsTyping] = useState(false);

	const sampleResponses = [
		'🍽️ Found 12 cozy cafes within 2km! Here are the top 3 based on ratings and distance: Starbucks (4.5⭐, 0.3km), Local Coffee House (4.7⭐, 0.5km), Bean & Brew (4.6⭐, 0.8km)',
		'🏛️ Great choice! Istanbul has amazing historical sites. Top recommendations: Hagia Sophia (9.2⭐), Blue Mosque (9.1⭐), Topkapi Palace (8.9⭐), Grand Bazaar (8.7⭐)',
		'🍕 Found 8 excellent restaurants nearby! Pizza Palace (4.8⭐, Italian), Sushi Master (4.9⭐, Japanese), Local Kebab House (4.6⭐, Turkish), Fine Dining Co (4.7⭐, International)',
		'🧘 Perfect for wellness! Found 5 great spots: Zen Yoga Studio (4.9⭐, 0.4km), Spa Retreat (4.8⭐, 0.7km), Meditation Center (4.7⭐, 1.2km)',
	];

	const handleSendMessage = () => {
		if (!inputText.trim()) return;

		// Add user message
		const userMessage: Message = {
			id: Date.now().toString(),
			text: inputText,
			isUser: true,
			timestamp: new Date(),
		};

		setMessages((prev) => [...prev, userMessage]);
		setInputText('');
		setIsTyping(true);

		// Simulate AI response
		setTimeout(() => {
			const randomResponse =
				sampleResponses[Math.floor(Math.random() * sampleResponses.length)];
			const aiMessage: Message = {
				id: (Date.now() + 1).toString(),
				text: randomResponse,
				isUser: false,
				timestamp: new Date(),
			};

			setMessages((prev) => [...prev, aiMessage]);
			setIsTyping(false);
		}, 1500);
	};

	const handleKeyPress = (e: React.KeyboardEvent) => {
		if (e.key === 'Enter' && !e.shiftKey) {
			e.preventDefault();
			handleSendMessage();
		}
	};

	return (
		<div
			className='p-6 md:p-8 border backdrop-blur-sm'
			style={{
				background: `linear-gradient(135deg, ${colors.brand.primary}10 0%, ${colors.neutral.cloudWhite}90 100%)`,
				borderColor: colors.brand.primary,
				boxShadow: `0 12px 40px ${colors.brand.primary}20`,
			}}>
			<div className='text-center mb-6'>
				<h3
					className='text-xl md:text-2xl font-bold mb-2'
					style={{ color: colors.brand.primary }}>
					💬 Live AI Chat Demo
				</h3>
				<p
					className='text-sm md:text-base'
					style={{ color: colors.neutral.slateGray }}>
					Ask me anything about places and locations!
				</p>
			</div>

			{/* Chat Interface */}
			<div className='bg-white rounded-lg shadow-inner max-w-2xl mx-auto'>
				{/* Messages */}
				<div className='h-64 overflow-y-auto p-4 space-y-3'>
					{messages.map((message) => (
						<div
							key={message.id}
							className={`flex ${
								message.isUser ? 'justify-end' : 'justify-start'
							}`}>
							<div
								className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
									message.isUser ? 'text-white' : 'bg-gray-100 text-gray-800'
								}`}
								style={{
									backgroundColor: message.isUser
										? colors.brand.primary
										: undefined,
								}}>
								<p className='text-sm'>{message.text}</p>
							</div>
						</div>
					))}
					{isTyping && (
						<div className='flex justify-start'>
							<div className='bg-gray-100 px-4 py-2 rounded-lg'>
								<div className='flex space-x-1'>
									<div className='w-2 h-2 bg-gray-400 rounded-full animate-bounce'></div>
									<div
										className='w-2 h-2 bg-gray-400 rounded-full animate-bounce'
										style={{ animationDelay: '0.1s' }}></div>
									<div
										className='w-2 h-2 bg-gray-400 rounded-full animate-bounce'
										style={{ animationDelay: '0.2s' }}></div>
								</div>
							</div>
						</div>
					)}
				</div>

				{/* Input */}
				<div className='border-t p-4'>
					<div className='flex space-x-2'>
						<input
							type='text'
							value={inputText}
							onChange={(e) => setInputText(e.target.value)}
							onKeyPress={handleKeyPress}
							placeholder='Ask me about places...'
							className='flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500'
						/>
						<button
							onClick={handleSendMessage}
							disabled={!inputText.trim() || isTyping}
							className='px-4 py-2 text-white rounded-lg hover:opacity-90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors'
							style={{ backgroundColor: colors.brand.primary }}>
							Send
						</button>
					</div>
				</div>
			</div>
		</div>
	);
};

export default ChatDemo;
