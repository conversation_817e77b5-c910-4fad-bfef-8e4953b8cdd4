
> wizlop@0.1.0 build:webpack
> webpack --config config/webpack/webpack.dev.js

asset bundle.js 4.48 MiB [emitted] [big] (name: main)
runtime modules 1.04 KiB 5 modules
modules by path ./node_modules/ 1.48 MiB 109 modules
modules by path ./app/ 116 KiB
  modules by path ./app/landing/components/ 79.7 KiB 17 modules
  modules by path ./app/landing/utils/*.ts 14.2 KiB
    ./app/landing/utils/scrollAnimations.ts 5 KiB [built] [code generated]
    ./app/landing/utils/responsiveUtils.ts 9.25 KiB [built] [code generated]
  ./app/page.tsx 1.13 KiB [built] [code generated]
  ./app/colors.ts 5.42 KiB [built] [code generated]
  ./app/shared/poi/constants.ts 16 KiB [built] [code generated]

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/features/TechnicalShowcase.tsx
./app/landing/components/features/TechnicalShowcase.tsx 16:28-45
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/features/TechnicalShowcase.tsx(16,29)
      TS2552: Cannot find name 'useViewportHeight'. Did you mean 'viewportHeight'?
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/features/TechnicalShowcase.tsx(16,29)
      TS2552: Cannot find name 'useViewportHeight'. Did you mean 'viewportHeight'?
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/LandingPage.tsx 6:0-61 25:100-117
 @ ./app/landing/components/index.ts 3:0-55 3:0-55
 @ ./app/page.tsx 6:0-51 22:16-27

ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/features/TechnologySummary.tsx
./app/landing/components/features/TechnologySummary.tsx 24:28-45
[tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/features/TechnologySummary.tsx(24,29)
      TS2552: Cannot find name 'useViewportHeight'. Did you mean 'viewportHeight'?
ts-loader-default_e552be7a13a4e4fd
Error: [tsl] ERROR in /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/app/landing/components/features/TechnologySummary.tsx(24,29)
      TS2552: Cannot find name 'useViewportHeight'. Did you mean 'viewportHeight'?
    at makeError (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:105:19)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:77:27
    at Array.map (<anonymous>)
    at formatErrors (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/utils.js:60:14)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:152:66
    at Array.forEach (<anonymous>)
    at provideErrorsToWebpack (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:149:31)
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/after-compile.js:36:9
    at /Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/ts-loader/dist/instances.js:214:13
    at fn (/Users/<USER>/Documents/Gospel_Of_Technology/Wizlop/application/node_modules/webpack/lib/Compilation.js:548:10)
 @ ./app/landing/components/index.ts 10:0-76 10:0-76
 @ ./app/page.tsx 6:0-51 22:16-27

webpack 5.100.1 compiled with 2 errors in 3894 ms
