/** @format */

import { colors } from '@/app/colors';
import React from 'react';
import { useSectionTransition } from '../utils/scrollAnimations';
import DynamicSectionContainer from './DynamicSectionContainer';

import TechnicalShowcase from './features/TechnicalShowcase';
import TechnologySummary from './features/TechnologySummary';
import LandingFooter from './footer/LandingFooter';
import HeroSection from './hero/HeroSection';
import WaveTransition from './WaveTransition';

interface LandingPageProps {
	onGetStarted: () => void;
}

const LandingPage: React.FC<LandingPageProps> = ({ onGetStarted }) => {
	// Section transition animations
	const heroTransition = useSectionTransition('hero', 'fade-scale');

	const technologySummaryTransition = useSectionTransition(
		'technology-summary',
		'fade-scale'
	);
	const techTransition = useSectionTransition('tech', 'fade-scale');
	const footerTransition = useSectionTransition('footer', 'fade-scale');

	return (
		<div className='min-h-screen h-full relative overflow-hidden bg-transparent'>
			{/* Clean Modern Background - No floating elements */}
			{/* Hero Section - No top padding */}
			<section>
				<DynamicSectionContainer sectionType='hero'>
					<div ref={heroTransition.elementRef}>
						<HeroSection onGetStarted={onGetStarted} />
					</div>
				</DynamicSectionContainer>
			</section>

			{/* Wave Transition to Technology Summary */}
			<div className='relative'>
				<WaveTransition
					isActive={technologySummaryTransition.isVisible}
					direction='up'
					color={colors.brand.blue}
				/>
			</div>

			{/* Technology Summary Section */}
			<section className='py-8 md:py-12 lg:py-16'>
				<DynamicSectionContainer sectionType='section1'>
					<div
						ref={technologySummaryTransition.elementRef}
						className={`transition-all duration-300 ease-out ${
							technologySummaryTransition.animationState === 'idle'
								? 'opacity-0 transform translate-y-8 scale-95'
								: technologySummaryTransition.animationState === 'entering'
								? 'opacity-70 transform translate-y-4 scale-98'
								: 'opacity-100 transform translate-y-0 scale-100'
						}`}>
						<TechnologySummary />
					</div>
				</DynamicSectionContainer>
			</section>

			{/* Wave Transition to Technical Section */}
			<div className='relative'>
				<WaveTransition
					isActive={techTransition.isVisible}
					direction='up'
					color={colors.supporting.teal}
				/>
			</div>

			{/* Technical Showcase Section */}
			<section className='py-8 md:py-12 lg:py-16'>
				<DynamicSectionContainer sectionType='section1'>
					<div
						ref={techTransition.elementRef}
						className={`transition-all duration-300 ease-out ${
							techTransition.animationState === 'idle'
								? 'opacity-0 transform translate-y-8 scale-95'
								: techTransition.animationState === 'entering'
								? 'opacity-70 transform translate-y-4 scale-98'
								: 'opacity-100 transform translate-y-0 scale-100'
						}`}>
						<TechnicalShowcase onGetStarted={onGetStarted} />
					</div>
				</DynamicSectionContainer>
			</section>

			{/* Wave Transition to Footer - Bottom to Top */}
			<div className='relative'>
				<WaveTransition
					isActive={footerTransition.isVisible}
					direction='up'
					color={colors.supporting.lightBlue}
				/>
			</div>

			{/* Footer Section - Centered */}
			<section className='py-8 md:py-12 lg:py-16'>
				<DynamicSectionContainer sectionType='footer'>
					<div
						ref={footerTransition.elementRef}
						className={`transition-all duration-350 ease-out ${
							footerTransition.animationState === 'idle'
								? 'opacity-0 transform scale-90'
								: footerTransition.animationState === 'entering'
								? 'opacity-50 transform scale-95'
								: 'opacity-100 transform scale-100'
						}`}>
						<LandingFooter />
					</div>
				</DynamicSectionContainer>
			</section>
		</div>
	);
};

export default LandingPage;
