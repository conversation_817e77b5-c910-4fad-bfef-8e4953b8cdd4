/** @format */

'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

// Redirect component for old /pois route
export default function POIsRedirect() {
	const router = useRouter();

	useEffect(() => {
		// Redirect to new /explore route
		router.replace('/explore');
	}, [router]);

	// Show loading state while redirecting
	return (
		<div className='min-h-screen flex items-center justify-center'>
			<div className='text-center'>
				<div className='animate-spin h-8 w-8 border-2 border-blue-500 border-t-transparent rounded-full mx-auto mb-4'></div>
				<p className='text-gray-600'>Redirecting to Explore...</p>
			</div>
		</div>
	);
}
