/** @format */

'use client';

import { colors } from '@/app/colors';
import React from 'react';
import { FiMapPin, FiSearch, FiZap } from 'react-icons/fi';

interface TechnologySummaryProps {
	onGetStarted?: () => void;
}

interface TechBenefit {
	id: string;
	icon: React.ReactNode;
	title: string;
	description: string;
	color: string;
}

const TechnologySummary: React.FC<TechnologySummaryProps> = ({
	onGetStarted,
}) => {
	const techBenefits: TechBenefit[] = [
		{
			id: 'instant-search',
			icon: <FiZap className='w-6 h-6 md:w-8 md:h-8' />,
			title: 'Instant search powered by spatial tech',
			description:
				'Lightning-fast results that understand exactly where you are and what you need.',
			color: colors.brand.blue,
		},
		{
			id: 'location-filtering',
			icon: <FiMapPin className='w-6 h-6 md:w-8 md:h-8' />,
			title: 'Location-based filtering with smart results',
			description:
				'Intelligent filtering that shows you the most relevant places based on your location and preferences.',
			color: colors.brand.green,
		},
		{
			id: 'ai-understanding',
			icon: <FiSearch className='w-6 h-6 md:w-8 md:h-8' />,
			title: "AI understands what you're looking for",
			description:
				'Natural conversation that gets smarter with every search, delivering exactly what you need.',
			color: colors.brand.navy,
		},
	];

	return (
		<div className='bg-transparent viewport-section relative min-h-screen py-8 md:py-12 lg:py-16'>
			{/* Background Elements */}
			<div className='absolute inset-0 opacity-5'>
				{/* Subtle geometric patterns */}
				<div
					className='absolute top-1/4 left-1/4 w-32 h-32 animate-pulse'
					style={{
						background: `radial-gradient(circle, ${colors.brand.blue}30 0%, transparent 70%)`,
						animation: 'pulse 6s ease-in-out infinite',
					}}
				/>
				<div
					className='absolute bottom-1/4 right-1/4 w-24 h-24 animate-pulse'
					style={{
						background: `linear-gradient(45deg, ${colors.brand.green}20 0%, transparent 70%)`,
						transform: 'rotate(45deg)',
						animation: 'pulse 4s ease-in-out infinite',
						animationDelay: '2s',
					}}
				/>
			</div>

			{/* Main Content */}
			<div className='relative z-10 w-full px-4 md:px-6 lg:px-12 max-w-7xl mx-auto'>
				{/* Section Header */}
				<div className='text-center mb-8 md:mb-12 lg:mb-16'>
					<div className='flex justify-center mb-4 md:mb-6'>
						<div
							className='inline-flex items-center space-x-2 px-4 py-2 md:px-6 md:py-3 border backdrop-blur-sm rounded-full'
							style={{
								background: `linear-gradient(135deg, ${colors.ui.blue50}80 0%, ${colors.ui.green50}80 100%)`,
								borderColor: colors.ui.gray200,
								boxShadow: '0 4px 16px rgba(51, 194, 255, 0.08)',
							}}>
							<FiZap
								className='w-4 h-4 md:w-5 md:h-5'
								style={{ color: colors.brand.blue }}
							/>
							<span
								className='text-sm md:text-base font-medium'
								style={{ color: colors.neutral.textBlack }}>
								Smart Technology
							</span>
						</div>
					</div>

					<h2
						className='text-2xl md:text-3xl lg:text-4xl font-bold mb-4 md:mb-6'
						style={{ color: colors.brand.navy }}>
						Smart tech that finds great places — fast.
					</h2>

					<p
						className='text-base md:text-lg lg:text-xl max-w-3xl mx-auto leading-relaxed'
						style={{ color: colors.neutral.slateGray }}>
						Experience the power of advanced location technology designed to
						make discovery effortless and enjoyable.
					</p>
				</div>

				{/* Technology Benefits Grid */}
				<div className='grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8 lg:gap-12'>
					{techBenefits.map((benefit, index) => (
						<div
							key={benefit.id}
							className='group relative p-6 md:p-8 border backdrop-blur-sm transition-all duration-300 hover:scale-105'
							style={{
								background: `linear-gradient(135deg, ${colors.neutral.cloudWhite}95 0%, ${colors.ui.blue50}30 100%)`,
								borderColor: colors.ui.gray200,
								boxShadow: '0 8px 32px rgba(51, 194, 255, 0.08)',
								animationDelay: `${index * 200}ms`,
								animation: 'fadeInUp 0.8s ease-out forwards',
							}}>
							{/* Icon */}
							<div
								className='w-12 h-12 md:w-16 md:h-16 flex items-center justify-center mb-4 md:mb-6 mx-auto group-hover:scale-110 transition-transform duration-300'
								style={{
									background: `linear-gradient(135deg, ${benefit.color} 0%, ${benefit.color}80 100%)`,
									borderRadius: '16px',
								}}>
								<div style={{ color: 'white' }}>{benefit.icon}</div>
							</div>

							{/* Content */}
							<div className='text-center'>
								<h3
									className='text-lg md:text-xl font-bold mb-3 md:mb-4'
									style={{ color: colors.brand.navy }}>
									{benefit.title}
								</h3>
								<p
									className='text-sm md:text-base leading-relaxed'
									style={{ color: colors.neutral.slateGray }}>
									{benefit.description}
								</p>
							</div>

							{/* Hover Effect */}
							<div
								className='absolute inset-0 opacity-0 group-hover:opacity-10 transition-opacity duration-300 rounded-lg'
								style={{
									background: `linear-gradient(135deg, ${benefit.color} 0%, transparent 100%)`,
								}}
							/>
						</div>
					))}
				</div>
			</div>
		</div>
	);
};

export default TechnologySummary;
