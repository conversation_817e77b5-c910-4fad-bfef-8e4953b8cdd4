/** @format */

'use client';

import { colors } from '@/app/colors';
import dynamic from 'next/dynamic';
import React, {
	forwardRef,
	useCallback,
	useEffect,
	useImperativeHandle,
	useRef,
	useState,
} from 'react';
import POIRankingPanel from './components/POIRankingPanel';
import './ranking.css';

// Global zoom constants - all functions use these values
const ZOOM_CONSTANTS = {
	INITIAL_ALTITUDE: 1.7,
	TRANSITION_THRESHOLD: 0.5, // Trigger redirect to flat map page
	MANUAL_RETURN_PROTECTION_THRESHOLD: 200, // Reset manual return protection
	SAFE_DISTANCE: 1.2, // Safe distance for manual return protection
} as const;

// Dynamically import Globe component to avoid SSR issues
const RankingGL = dynamic(() => import('react-globe.gl'), {
	ssr: false,
	loading: () => (
		<div className='w-full h-full flex items-center justify-center'>
			<div className='text-center'>
				<div
					className='animate-spin h-12 w-12 border-t-2 border-b-2 mx-auto mb-4'
					style={{
						borderTopColor: colors.brand.blue,
						borderBottomColor: colors.brand.blue,
						borderLeftColor: 'transparent',
						borderRightColor: 'transparent',
					}}></div>
				<p
					className='text-base md:text-lg font-medium'
					style={{ color: colors.neutral.textBlack }}>
					Loading Interactive Ranking...
				</p>
			</div>
		</div>
	),
});

interface POI {
	id: number;
	poi_type: string;
	name: string;
	category: string;
	subcategory: string;
	city: string;
	district: string;
	country?: string;
	latitude: number;
	longitude: number;
	random_score: number;
}

interface RankingProps {
	isClient: boolean;
	countriesData: Array<Record<string, unknown>>;
	userMarker: Array<Record<string, unknown>>;
	currentZoom: number;
	currentLocation: { lat: number; lng: number } | null;
	userLocation: { lat: number; lng: number } | null;
	onZoomChange: (distance: number) => void;
	onLocationChange: (location: { lat: number; lng: number }) => void;
	onLocationInfoToggle: () => void;
	isManuallyReturning: boolean;
	isTransitioning: boolean;
	showNavButtons: boolean;
	manualReturnTime: number;
	isManuallyReturningRef: React.MutableRefObject<boolean>;
}

export interface RankingRef {
	pointOfView: (
		pov: { lat: number; lng: number; altitude: number },
		duration?: number
	) => void;
	controls: () => GlobeControls | undefined;
	getCoords: (
		x: number,
		y: number
	) => { lat: number; lng: number } | null | undefined;
}

// Internal globe instance interface for react-globe.gl
interface GlobeControls {
	autoRotate: boolean;
	autoRotateSpeed: number;
	enableZoom: boolean;
	enableRotate: boolean;
	enablePan: boolean;
	minDistance: number;
	maxDistance: number;
	enableDamping: boolean;
	dampingFactor: number;
	rotateSpeed: number;
	zoomSpeed: number;
	addEventListener: (event: string, handler: () => void) => void;
	removeEventListener: (event: string, handler: () => void) => void;
}

interface RankingInstance {
	pointOfView: (
		pov?: { lat: number; lng: number; altitude: number },
		duration?: number
	) => { lat: number; lng: number; altitude: number };
	controls: () => GlobeControls;
	getCoords: (
		x: number,
		y: number
	) => { lat: number; lng: number } | null | undefined;
	width: (width?: number) => RankingInstance | number;
	height: (height?: number) => RankingInstance | number;
	parentElement?: HTMLElement;
}

const Ranking = forwardRef<RankingRef, RankingProps>(
	(
		{
			isClient,
			countriesData,
			userMarker,
			currentZoom,
			currentLocation,
			userLocation,
			onZoomChange,
			onLocationChange,
			onLocationInfoToggle,
			isManuallyReturning,
			isTransitioning,
			isManuallyReturningRef,
		},
		ref
	) => {
		const rankingRef = useRef<RankingInstance | null>(null);
		const containerRef = useRef<HTMLDivElement>(null);

		// Game-like state
		const [rankingPOIs, setRankingPOIs] = useState<POI[]>([]);
		const [showRankingPanel, setShowRankingPanel] = useState(false);
		const [selectedLocation, setSelectedLocation] = useState<{
			name: string;
			type: 'country' | 'city';
		} | null>(null);
		const [isLoadingRankings, setIsLoadingRankings] = useState(false);

		// Responsive sizing state - start with reasonable default
		const [globeSize, setGlobeSize] = useState({ width: 800, height: 600 });

		// Simple globe configuration
		const [globeColor, setGlobeColor] = useState<string>(colors.brand.blue);
		const globeTexture =
			'https://unpkg.com/three-globe/example/img/earth-day.jpg';

		// Full screen sizing effect
		useEffect(() => {
			const updateGlobeSize = () => {
				// Give globe full screen access
				const screenWidth = window.innerWidth;
				const screenHeight = window.innerHeight;

				// Use full screen dimensions
				setGlobeSize({ width: screenWidth, height: screenHeight });
			};

			// Run immediately and on resize
			updateGlobeSize();
			window.addEventListener('resize', updateGlobeSize);

			return () => window.removeEventListener('resize', updateGlobeSize);
		}, []);

		// Smooth color transition effect
		useEffect(() => {
			// Example: cycle through brand/supporting colors every 10s
			const palette = [
				colors.brand.blue,
				colors.brand.navy,
				colors.brand.green,
				colors.supporting.lightBlue,
				colors.supporting.mintGreen,
			];
			let idx = 0;
			const interval = setInterval(() => {
				idx = (idx + 1) % palette.length;
				setGlobeColor(palette[idx]);
			}, 10000);
			return () => clearInterval(interval);
		}, []);

		// Expose ranking methods to parent
		useImperativeHandle(ref, () => ({
			pointOfView: (
				pov: { lat: number; lng: number; altitude: number },
				duration?: number
			) => {
				if (rankingRef.current) {
					rankingRef.current.pointOfView(pov, duration);
				}
			},
			controls: () => rankingRef.current?.controls(),
			getCoords: (x: number, y: number) => rankingRef.current?.getCoords(x, y),
		}));

		// Game-like country coloring - only Turkey is unlocked
		const getCountryColor = (countryName: string) => {
			const name = countryName.toLowerCase();

			// Turkey is unlocked (bright colors)
			if (name.includes('turkey') || name.includes('türkiye')) {
				return '#4CAF50'; // Green for unlocked
			}

			// All other countries are locked (dark colors)
			return '#2C2C2C'; // Dark gray for locked
		};

		// Handle city selection from dropdown
		const handleCitySelect = useCallback((cityName: string) => {
			console.log('🏙️ City selected:', cityName);
			// You can add logic here to fetch POIs for the specific city
			// For now, we'll just log the selection
		}, []);

		// Fetch POI rankings for clicked location
		const fetchLocationRankings = useCallback(
			async (locationName: string, locationType: 'country' | 'city') => {
				if (!currentLocation) {
					console.warn('No current location available');
					return;
				}

				console.log('🚀 Fetching rankings for:', locationName, locationType);

				try {
					const response = await fetch('/api/pois/rankings', {
						method: 'POST',
						headers: { 'Content-Type': 'application/json' },
						credentials: 'include', // ✅ Include cookies for authentication
						body: JSON.stringify({
							locationType,
							locationName,
							center: currentLocation,
							zoom: currentZoom,
							offset: 0,
							limit: 20,
							country: locationType === 'country' ? locationName : undefined,
							includeInteractions: true, // ✅ Include interaction data
						}),
					});

					if (response.ok) {
						const data = await response.json();
						if (data.success) {
							console.log(
								'✅ Rankings loaded:',
								data.pois?.length || 0,
								'POIs'
							);
							setRankingPOIs(data.pois || []);
							// Panel is already shown from click handler
						} else {
							console.error('❌ API returned error:', data.error);
							setShowRankingPanel(false);
						}
					} else {
						console.error('❌ API request failed:', response.status);
						setShowRankingPanel(false);
					}
				} catch (error) {
					console.error('❌ Failed to fetch location rankings:', error);
					setShowRankingPanel(false);
				} finally {
					setIsLoadingRankings(false);
				}
			},
			[currentLocation, currentZoom]
		);

		// Handle country click
		const handlePolygonClick = useCallback(
			(polygon: { properties?: Record<string, unknown> }) => {
				console.log('🎯 Polygon clicked:', polygon);

				const countryName = String(
					polygon.properties?.NAME ||
						polygon.properties?.name ||
						polygon.properties?.NAME_EN ||
						''
				);

				if (!countryName) {
					console.warn('No country name found in polygon:', polygon);
					return;
				}

				// For now, only allow interactions with Turkey (unlocked country)
				const isTurkey =
					countryName.toLowerCase().includes('turkey') ||
					countryName.toLowerCase().includes('türkiye');

				if (isTurkey) {
					console.log('🇹🇷 Turkey clicked, fetching rankings...');
					// Use English name - database function now handles both English and Turkish
					const countryNameForAPI = 'Turkey';

					// Immediately show loading state and panel
					setIsLoadingRankings(true);
					setShowRankingPanel(true);
					setSelectedLocation({ name: countryNameForAPI, type: 'country' });
					setRankingPOIs([]); // Clear previous data

					// Fetch data with a small delay to ensure UI updates first
					setTimeout(() => {
						fetchLocationRankings(countryNameForAPI, 'country');
					}, 50);
				} else {
					// Show locked country message
					alert(
						`🔒 ${countryName} is locked! Only Turkey is currently unlocked in this version.`
					);
				}
			},
			[fetchLocationRankings]
		);

		// Configure globe
		useEffect(() => {
			if (!isClient) return;

			const initializeRanking = () => {
				const ranking = rankingRef.current;
				if (!ranking) return false;

				try {
					if (!ranking.controls || typeof ranking.controls !== 'function') {
						return false;
					}

					// Configure interactive ranking settings
					const controls = ranking.controls();
					controls.enableZoom = true;
					controls.enableRotate = true;
					controls.autoRotate = false;
					controls.minDistance = 60;
					controls.maxDistance = 800;
					controls.enablePan = true;
					controls.enableDamping = true;
					controls.dampingFactor = 0.1;
					controls.rotateSpeed = 0.5;
					controls.zoomSpeed = 1.0;

					// Set initial position
					const initialLat = userLocation?.lat || currentLocation?.lat || 0;
					const initialLng = userLocation?.lng || currentLocation?.lng || 0;

					ranking.pointOfView({
						lat: initialLat,
						lng: initialLng,
						altitude: ZOOM_CONSTANTS.INITIAL_ALTITUDE,
					});

					// Set initial size to full screen
					const updateRankingSize = () => {
						if (ranking && rankingRef.current?.parentElement) {
							const width = window.innerWidth;
							const height = window.innerHeight;
							ranking.width(width);
							ranking.height(height);
						}
					};

					updateRankingSize();

					// Handle window resize
					const handleResize = () => {
						updateRankingSize();
					};

					window.addEventListener('resize', handleResize);

					// Handle view changes (zoom, rotation, pan) with throttling
					let lastUpdate = 0;
					const handleViewChange = () => {
						const now = Date.now();
						if (now - lastUpdate < 50) return; // Throttle to 20fps
						lastUpdate = now;

						if (!ranking) return;

						const pov = ranking.pointOfView();
						const distance = pov.altitude;

						// Debug zoom level
						console.log('Current zoom level:', distance);

						onZoomChange(distance);

						// Update current view coordinates on any change
						try {
							const center = ranking.getCoords(
								window.innerWidth / 2,
								window.innerHeight / 2
							);
							if (
								center &&
								typeof center.lat === 'number' &&
								typeof center.lng === 'number'
							) {
								onLocationChange({ lat: center.lat, lng: center.lng });
							}
						} catch (error) {
							console.warn('Failed to get globe coordinates:', error);
						}

						// Direct transition trigger as backup for very close zoom
						const isCurrentlyReturning =
							isManuallyReturning || isManuallyReturningRef.current;

						// Auto-redirect to flat-map page when zooming in close
						if (
							distance < ZOOM_CONSTANTS.TRANSITION_THRESHOLD &&
							!isTransitioning &&
							!isCurrentlyReturning
						) {
							console.log('🚀 Redirecting to flat-map at distance:', distance);
							// Redirect to flat-map page instead of embedded transition
							window.location.href = '/flat-map';
						} else if (
							isCurrentlyReturning &&
							distance < ZOOM_CONSTANTS.TRANSITION_THRESHOLD
						) {
							console.log(
								'🛡️ Auto-redirect blocked - manual return protection active'
							);
						}
					};

					// Add event listener
					ranking.controls().addEventListener('change', handleViewChange);

					const resizeObserver = new ResizeObserver(() => {
						updateRankingSize();
					});

					const container = rankingRef.current?.parentElement;
					if (container) resizeObserver.observe(container);

					// Return cleanup function
					const cleanup = () => {
						window.removeEventListener('resize', handleResize);
						ranking.controls().removeEventListener('change', handleViewChange);
						resizeObserver.disconnect();
					};

					return cleanup;
				} catch {
					return false;
				}
			};

			// Try to initialize immediately
			let cleanup = initializeRanking();

			// If initialization failed, retry with intervals
			if (!cleanup) {
				const retryInterval = setInterval(() => {
					cleanup = initializeRanking();
					if (cleanup) {
						clearInterval(retryInterval);
					}
				}, 100);

				return () => {
					clearInterval(retryInterval);
					if (cleanup && typeof cleanup === 'function') {
						cleanup();
					}
				};
			}

			return cleanup;
		}, [isClient]); // Only re-initialize when client state changes

		if (!isClient) return null;

		return (
			<>
				<div
					ref={containerRef}
					className='absolute inset-0 w-full h-full opacity-100 scale-100'>
					<div
						className='w-full h-full ranking-container flex items-center justify-center'
						style={{ minHeight: 320, minWidth: 320 }}>
						<RankingGL
							// eslint-disable-next-line @typescript-eslint/no-explicit-any
							ref={rankingRef as React.MutableRefObject<any>}
							globeImageUrl={globeTexture}
							backgroundColor='rgba(0, 0, 0, 0)'
							showGlobe={true}
							showAtmosphere={true}
							atmosphereColor={globeColor + '33'}
							atmosphereAltitude={0.18}
							globeMaterial={{
								transparent: true,
								opacity: 0.92,
								color: globeColor,
								transition: 'color 1.5s cubic-bezier(0.4,0,0.2,1)',
							}}
							enablePointerInteraction={true}
							rendererConfig={{
								antialias: true,
								alpha: true,
								shadowMap: {
									enabled: true,
								},
							}}
							animateIn={true}
							width={globeSize.width}
							height={globeSize.height}
							// Country polygons with game-like colors
							polygonsData={countriesData}
							polygonCapColor={(d: {
								properties?: Record<string, unknown>;
							}) => {
								const countryName = String(
									d.properties?.NAME ||
										d.properties?.name ||
										d.properties?.NAME_EN ||
										'unknown'
								);
								return getCountryColor(countryName);
							}}
							polygonSideColor={(d: {
								properties?: Record<string, unknown>;
							}) => {
								const countryName = String(
									d.properties?.NAME ||
										d.properties?.name ||
										d.properties?.NAME_EN ||
										'unknown'
								);
								return getCountryColor(countryName);
							}}
							polygonStrokeColor={() => {
								return '#FFFFFF'; // White boundaries for all countries
							}}
							polygonAltitude={() => 0.01}
							onPolygonClick={(polygon: {
								properties?: Record<string, unknown>;
							}) => {
								console.log('🎯 Globe polygon clicked:', polygon);
								// Handle country click
								handlePolygonClick(polygon);
							}}
							hexPolygonResolution={3}
							// Only user location marker (no POI markers)
							pointsData={userMarker}
							pointAltitude={() => 0.08}
							pointRadius={() => {
								const baseRadius = 0.3;
								const zoomFactor = Math.max(0.1, currentZoom / 800);
								return baseRadius * zoomFactor;
							}}
							pointColor={() => '#3B82F6'}
							pointResolution={12}
							pointLabel={() => 'Your Location - Click for details'}
							onPointClick={() => onLocationInfoToggle()}
							// Remove location labels to avoid screen-fixed text
							labelsData={[]}
							labelText={() => ''}
							labelSize={() => 0}
							labelAltitude={() => 0}
							labelColor={() => 'transparent'}
							labelResolution={0}
							labelIncludeDot={false}
							// Remove country name labels to avoid screen-fixed text
							polygonLabel={() => ''}
						/>
					</div>
				</div>

				{/* POI Ranking Panel */}
				<POIRankingPanel
					isVisible={showRankingPanel}
					onClose={() => setShowRankingPanel(false)}
					locationName={selectedLocation?.name || ''}
					locationType={selectedLocation?.type || 'country'}
					pois={rankingPOIs}
					isLoading={isLoadingRankings}
					onCitySelect={handleCitySelect}
					hasMoreData={true}
				/>
			</>
		);
	}
);

Ranking.displayName = 'Ranking';

export default Ranking;
