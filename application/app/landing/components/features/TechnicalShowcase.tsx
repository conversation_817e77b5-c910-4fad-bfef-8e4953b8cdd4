/** @format */

'use client';

import { colors } from '@/app/colors';
import { useViewportHeight } from '@/app/landing/utils/responsiveUtils';
import React, { useState } from 'react';
import {
	FiChevronDown,
	FiChevronUp,
	FiCpu,
	FiTarget,
	FiZap,
} from 'react-icons/fi';

interface TechnicalShowcaseProps {
	onGetStarted: () => void;
}

interface TechFeature {
	id: string;
	icon: React.ReactNode;
	title: string;
	subtitle: string;
	description: string;
	metrics: string;
	color: string;
	gradient: string;
}

const TechnicalShowcase: React.FC<TechnicalShowcaseProps> = ({
	onGetStarted,
}) => {
	const { screenSize, viewportHeight, padding } = useViewportHeight('section');
	// Use full viewport height for sections to match browser window
	const sectionMinHeight = viewportHeight > 0 ? `${viewportHeight}px` : '100vh';
	const [expandedSections, setExpandedSections] = useState<string[]>([]);

	const toggleSection = (sectionId: string) => {
		setExpandedSections((prev) =>
			prev.includes(sectionId)
				? prev.filter((id) => id !== sectionId)
				: [...prev, sectionId]
		);
	};

	// Simplified tech features - only 3 short summaries
	const techFeatures: TechFeature[] = [
		{
			id: 'postgis-search',
			icon: <FiZap className='w-6 h-6' />,
			title: 'PostGIS-powered search',
			subtitle: 'Lightning-fast spatial queries',
			description:
				'Advanced spatial database technology delivers instant search results across millions of locations.',
			metrics: '< 0.1s response',
			color: colors.brand.blue,
			gradient: `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.supporting.lightBlue} 100%)`,
		},
		{
			id: 'context-ai',
			icon: <FiTarget className='w-6 h-6' />,
			title: 'Context-aware AI queries',
			subtitle: 'Smart understanding',
			description:
				'AI technology that understands natural language and provides personalized, relevant results.',
			metrics: 'Smart matching',
			color: colors.brand.green,
			gradient: `linear-gradient(135deg, ${colors.brand.green} 0%, ${colors.supporting.mintGreen} 100%)`,
		},
		{
			id: 'smart-ranking',
			icon: <FiCpu className='w-6 h-6' />,
			title: 'Smart ranking with distance + preferences',
			subtitle: 'Intelligent scoring',
			description:
				'Sophisticated algorithms that balance distance, relevance, and user preferences for optimal results.',
			metrics: 'Multi-factor scoring',
			color: colors.brand.navy,
			gradient: `linear-gradient(135deg, ${colors.brand.navy} 0%, ${colors.supporting.darkBlue} 100%)`,
		},
	];

	return (
		<div
			className='bg-transparent viewport-section relative'
			style={{
				minHeight: sectionMinHeight,
				paddingLeft: 0,
				paddingRight: 0,
				paddingTop: padding,
				paddingBottom: '120px',
			}}>
			{/* Background Elements */}
			<div className='absolute inset-0 opacity-5'>
				<div
					className='absolute top-1/4 left-1/4 w-32 h-32 animate-pulse'
					style={{
						background: `radial-gradient(circle, ${colors.brand.blue}40 0%, transparent 70%)`,
						animationDuration: '4s',
					}}
				/>
				<div
					className='absolute bottom-1/3 right-1/4 w-24 h-24 animate-pulse'
					style={{
						background: `linear-gradient(45deg, ${colors.brand.green}30 0%, transparent 70%)`,
						transform: 'rotate(45deg)',
						animationDuration: '3s',
						animationDelay: '1s',
					}}
				/>
			</div>

			<div className='relative z-10 w-full px-4 md:px-6 lg:px-12 max-w-7xl mx-auto'>
				{/* Section Title */}
				<div className='text-center mb-8 md:mb-12'>
					<h2
						className='text-xl md:text-2xl lg:text-3xl font-bold mb-4 md:mb-6'
						style={{ color: colors.brand.navy }}>
						Advanced Technology
					</h2>
					<p
						className='text-base md:text-lg max-w-3xl mx-auto leading-relaxed'
						style={{ color: colors.neutral.slateGray }}>
						Our platform combines cutting-edge spatial technology with
						intelligent algorithms
					</p>
				</div>

				{/* Individual Collapsible Tech Features */}
				<div className='space-y-4 md:space-y-6 max-w-4xl mx-auto'>
					{techFeatures.map((feature, index) => {
						const isExpanded = expandedSections.includes(feature.id);
						return (
							<div
								key={feature.id}
								className='border backdrop-blur-sm transition-all duration-300'
								style={{
									background: `linear-gradient(135deg, ${colors.neutral.cloudWhite}95 0%, ${colors.ui.blue50}30 100%)`,
									borderColor: colors.ui.gray200,
									boxShadow: isExpanded
										? '0 12px 40px rgba(51, 194, 255, 0.15)'
										: '0 4px 16px rgba(51, 194, 255, 0.08)',
									borderRadius: '12px',
								}}>
								{/* Collapsible Header */}
								<button
									onClick={() => toggleSection(feature.id)}
									className='w-full flex items-center justify-between p-4 md:p-6 text-left transition-all duration-200 hover:bg-opacity-80'
									style={{
										background: 'transparent',
									}}>
									<div className='flex items-center space-x-3 md:space-x-4'>
										<div
											className='w-10 h-10 md:w-12 md:h-12 flex items-center justify-center flex-shrink-0'
											style={{
												background: feature.gradient,
												color: 'white',
												borderRadius: '8px',
											}}>
											{feature.icon}
										</div>
										<div>
											<h3
												className='text-base md:text-lg font-bold'
												style={{ color: colors.brand.navy }}>
												{feature.title}
											</h3>
											<p
												className='text-sm font-medium'
												style={{ color: feature.color }}>
												{feature.subtitle}
											</p>
										</div>
									</div>
									{isExpanded ? (
										<FiChevronUp
											className='w-5 h-5 transition-transform duration-200 flex-shrink-0'
											style={{ color: colors.brand.blue }}
										/>
									) : (
										<FiChevronDown
											className='w-5 h-5 transition-transform duration-200 flex-shrink-0'
											style={{ color: colors.brand.blue }}
										/>
									)}
								</button>

								{/* Collapsible Content */}
								{isExpanded && (
									<div
										className='px-4 md:px-6 pb-4 md:pb-6 transition-all duration-300'
										style={{
											borderTop: `1px solid ${colors.ui.gray200}`,
											marginTop: '-1px',
										}}>
										<div className='pt-4'>
											<p
												className='text-base leading-relaxed mb-4'
												style={{ color: colors.neutral.slateGray }}>
												{feature.description}
											</p>
											<div
												className='inline-block px-3 py-1 text-sm font-medium rounded-full'
												style={{
													background: `${feature.color}15`,
													color: feature.color,
												}}>
												{feature.metrics}
											</div>
										</div>
									</div>
								)}
							</div>
						);
					})}
				</div>
			</div>
		</div>
	);
};

export default TechnicalShowcase;
