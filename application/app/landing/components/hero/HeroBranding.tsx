/** @format */

'use client';

import { colors } from '@/app/colors';
import {
	getPOICategories,
	getPOISubcategories,
} from '@/app/shared/poi/constants';
import React, { useEffect, useState } from 'react';

// Utils

// Demo components
import ChatDemo from '../demos/ChatDemo';
import ExploreDemo from '../demos/ExploreDemo';
import RankingDemo from '../demos/RankingDemo';

// Hook for responsive screen size detection
const useScreenSize = () => {
	const [screenSize, setScreenSize] = useState<'mobile' | 'tablet' | 'desktop'>(
		'desktop'
	);

	useEffect(() => {
		const checkScreenSize = () => {
			const width = window.innerWidth;
			if (width < 768) {
				setScreenSize('mobile');
			} else if (width < 1024) {
				setScreenSize('tablet');
			} else {
				setScreenSize('desktop');
			}
		};

		checkScreenSize();
		window.addEventListener('resize', checkScreenSize);
		return () => window.removeEventListener('resize', checkScreenSize);
	}, []);

	return screenSize;
};

interface HeroBrandingProps {
	onGetStarted: () => void;
}

const HeroBranding: React.FC<HeroBrandingProps> = ({ onGetStarted }) => {
	// Get real data counts
	const categoriesCount = getPOICategories().length;
	const subcategoriesCount = getPOISubcategories().length;
	const [showInfoCards, setShowInfoCards] = useState(false);
	const [showDemoDropdown, setShowDemoDropdown] = useState(false);
	const [activeTab, setActiveTab] = useState<'chat' | 'explore' | 'ranking'>(
		'chat'
	);
	const screenSize = useScreenSize();
	const [letterAnimations, setLetterAnimations] = useState([
		false,
		false,
		false,
		false,
		false,
		false,
	]); // Controls individual letter animations for "Wizlop"

	// Letter-by-letter animation sequence
	useEffect(() => {
		const timer1 = setTimeout(() => {
			setShowInfoCards(true); // Show info cards at the same time as letter animations

			// Animate letters one by one: W-i-z-l-o-p
			const letters = ['W', 'i', 'z', 'l', 'o', 'p'];
			letters.forEach((_, index) => {
				setTimeout(() => {
					setLetterAnimations((prev) => {
						const newAnimations = [...prev];
						newAnimations[index] = true;
						return newAnimations;
					});
				}, index * 150); // 150ms delay between each letter
			});
		}, 500); // Start letter animations after 0.5 seconds

		return () => {
			clearTimeout(timer1);
		};
	}, []);

	// Handle demo dropdown toggle
	const handleDemoToggle = () => {
		setShowDemoDropdown(!showDemoDropdown);
	};

	// Handle tab change in demo dropdown
	const handleTabChange = (tab: 'chat' | 'explore' | 'ranking') => {
		setActiveTab(tab);
	};

	return (
		<div className='relative w-full'>
			{/* Compact Hero Layout - Following rules.txt */}
			<div className='relative overflow-hidden'>
				{/* Main Content - Compact Two Column Layout */}
				<div className='relative z-10 py-6 md:py-8 lg:py-10'>
					<div className='w-full px-4 md:px-6 lg:px-12 max-w-7xl mx-auto'>
						<div className='w-full'>
							{/* Full Width - Brand Identity & Demos */}
							<div className='flex items-center justify-center h-full'>
								<div className='w-full'>
									<div className='text-center w-full flex flex-col items-center space-y-4 md:space-y-5 lg:space-y-6'>
										{/* Brand Name - Centered, No Logo */}
										<div className='flex justify-center'>
											<h1
												className='font-black tracking-tight leading-none relative overflow-hidden'
												style={{
													fontSize:
														screenSize === 'mobile'
															? '2.5rem'
															: screenSize === 'tablet'
															? '3.5rem'
															: '4.5rem',
												}}>
												{/* Wizlop - Letter by letter animated entrance */}
												<span
													className='inline-block relative'
													style={{ zIndex: 5 }}>
													{['W', 'i', 'z', 'l', 'o', 'p'].map(
														(letter, index) => (
															<span
																key={index}
																className='inline-block relative'
																style={{
																	backgroundImage: `linear-gradient(135deg, ${colors.brand.secondary} 0%, ${colors.brand.primary} 50%, ${colors.brand.accent} 100%)`,
																	WebkitBackgroundClip: 'text',
																	WebkitTextFillColor: 'transparent',
																	backgroundClip: 'text',
																	transform: letterAnimations[index]
																		? 'translateX(0) scale(1)'
																		: 'translateX(-30px) scale(0.3)',
																	opacity: letterAnimations[index] ? 1 : 0,
																	transition:
																		'all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
																}}>
																{letter}
															</span>
														)
													)}
												</span>
											</h1>
										</div>

										{/* Tagline - Centered */}
										<div className='relative flex justify-center'>
											<p
												className='font-semibold tracking-wide text-center'
												style={{
													color: colors.brand.primary,
													fontSize:
														screenSize === 'mobile'
															? '1rem'
															: screenSize === 'tablet'
															? '1.25rem'
															: '1.5rem',
												}}>
												Explore Life. Experience Everything.
											</p>
										</div>

										{/* Three Main CTA Buttons - Horizontally Aligned */}
										<div
											className='relative transition-all duration-1000'
											style={{
												opacity: showInfoCards ? 1 : 0,
												transform: showInfoCards
													? 'translateY(0) scale(1)'
													: 'translateY(20px) scale(0.95)',
												transitionDelay: '200ms',
											}}>
											{/* Main Navigation Buttons */}
											<div className='flex flex-col sm:flex-row justify-center items-center gap-4 sm:gap-6 mb-8'>
												{/* Chat Button - Logo Primary Color */}
												<button
													onClick={() => {
														window.location.href = '/chat';
													}}
													className='group relative px-6 py-3 md:px-8 md:py-4 font-bold text-base md:text-lg lg:text-xl transition-all duration-200 transform hover:scale-105 active:scale-95 overflow-hidden w-full sm:w-auto min-w-[180px]'
													style={{
														background: colors.brand.primary, // #0EA5E9 - Sky Blue
														color: 'white',
														boxShadow: `0 6px 20px ${colors.brand.primary}40`,
													}}>
													<span className='relative z-10 flex items-center justify-center space-x-3'>
														<span className='text-2xl'>💬</span>
														<span>Chat</span>
													</span>
												</button>

												{/* Explore Button - Logo Accent Color */}
												<button
													onClick={() => {
														window.location.href = '/explore';
													}}
													className='group relative px-6 py-3 md:px-8 md:py-4 font-bold text-base md:text-lg lg:text-xl transition-all duration-200 transform hover:scale-105 active:scale-95 overflow-hidden w-full sm:w-auto min-w-[180px]'
													style={{
														background: colors.brand.accent, // #22C55E - Green
														color: 'white',
														boxShadow: `0 6px 20px ${colors.brand.accent}40`,
													}}>
													<span className='relative z-10 flex items-center justify-center space-x-3'>
														<span className='text-2xl'>🧭</span>
														<span>Explore</span>
													</span>
												</button>

												{/* Ranking Button - Supporting Color */}
												<button
													onClick={() => {
														window.location.href = '/ranking';
													}}
													className='group relative px-6 py-3 md:px-8 md:py-4 font-bold text-base md:text-lg lg:text-xl transition-all duration-200 transform hover:scale-105 active:scale-95 overflow-hidden w-full sm:w-auto min-w-[180px]'
													style={{
														background: colors.supporting['sup-color1'], // #FF6B6B - Vibrant Coral Red
														color: 'white',
														boxShadow: `0 6px 20px ${colors.supporting['sup-color1']}40`,
													}}>
													<span className='relative z-10 flex items-center justify-center space-x-3'>
														<span className='text-2xl'>📊</span>
														<span>Ranking</span>
													</span>
												</button>
											</div>

											{/* Demo Dropdown Button */}
											<div className='flex justify-center'>
												<button
													onClick={handleDemoToggle}
													className='px-6 py-3 font-medium transition-all duration-200 hover:scale-105 border-2'
													style={{
														background: showDemoDropdown
															? colors.brand.primary
															: 'transparent',
														color: showDemoDropdown
															? 'white'
															: colors.brand.primary,
														borderColor: colors.brand.primary,
													}}>
													{showDemoDropdown
														? '▼ Hide Demos'
														: '▶ Try Live Demos'}
												</button>
											</div>
										</div>

										{/* New Tagline Below Demos */}
										<div
											className='relative transition-all duration-1000'
											style={{
												opacity: showInfoCards ? 1 : 0,
												transform: showInfoCards
													? 'translateY(0) scale(1)'
													: 'translateY(20px) scale(0.95)',
												transitionDelay: '800ms',
											}}>
											<div className='relative flex justify-center'>
												<p
													className='font-medium tracking-wide text-center'
													style={{
														color: colors.neutral.slateGray,
														fontSize:
															screenSize === 'mobile'
																? '0.625rem'
																: screenSize === 'tablet'
																? '0.75rem'
																: '0.875rem',
													}}>
													Chat. Discover. Rank. All in one place.
												</p>
											</div>
										</div>

										{/* Demo Dropdown Container - Moved inside main content */}
										{showDemoDropdown && (
											<div
												className='w-full mt-6 md:mt-8 transition-all duration-500 ease-in-out'
												style={{
													opacity: showDemoDropdown ? 1 : 0,
													transform: showDemoDropdown
														? 'translateY(0)'
														: 'translateY(-20px)',
												}}>
												<div
													className='w-full max-w-5xl mx-auto bg-white/90 backdrop-blur-sm border border-gray-200/50 shadow-xl p-4 md:p-6 lg:p-8'
													style={{
														maxHeight: 'calc(100vh - 200px)',
														height: 'calc(100vh - 200px)',
														minHeight: '400px',
														position: 'relative',
													}}>
													{/* Tab Navigation */}
													<div className='flex justify-center mb-6'>
														<div className='flex gap-2'>
															{(['chat', 'explore', 'ranking'] as const).map(
																(tab) => (
																	<button
																		key={tab}
																		onClick={() => handleTabChange(tab)}
																		className='px-4 py-2 font-medium transition-all duration-200'
																		style={{
																			background:
																				activeTab === tab
																					? tab === 'chat'
																						? colors.brand.primary
																						: tab === 'explore'
																						? colors.brand.accent
																						: colors.supporting['sup-color1']
																					: colors.ui['gray-1'],
																			color:
																				activeTab === tab
																					? 'white'
																					: colors.neutral['text-primary'],
																			border: `1px solid ${colors.ui['gray-3']}`,
																		}}>
																		{tab === 'chat'
																			? '💬 Chat'
																			: tab === 'explore'
																			? '🧭 Explore'
																			: '📊 Ranking'}
																	</button>
																)
															)}
														</div>
													</div>

													{/* Demo Content */}
													<div className='h-full overflow-y-auto'>
														{activeTab === 'chat' && <ChatDemo />}
														{activeTab === 'explore' && <ExploreDemo />}
														{activeTab === 'ranking' && <RankingDemo />}
													</div>
												</div>
											</div>
										)}
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
};

export default HeroBranding;
