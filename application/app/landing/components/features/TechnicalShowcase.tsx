/** @format */

'use client';

import { colors } from '@/app/colors';
import { useViewportHeight } from '@/app/landing/utils/responsiveUtils';
import React, { useState } from 'react';
import { <PERSON><PERSON>pu, FiTarget, FiZap } from 'react-icons/fi';

interface TechnicalShowcaseProps {
	onGetStarted: () => void;
}

const TechnicalShowcase: React.FC<TechnicalShowcaseProps> = ({
	onGetStarted,
}) => {
	const { screenSize, viewportHeight, padding } = useViewportHeight('section');
	// Use full viewport height for sections to match browser window
	const sectionMinHeight = viewportHeight > 0 ? `${viewportHeight}px` : '100vh';
	const [showTechDropdown, setShowTechDropdown] = useState(false);
	const [activeTech, setActiveTech] = useState<'spatial' | 'ai' | 'agents'>(
		'spatial'
	);

	const handleTechToggle = () => {
		setShowTechDropdown(!showTechDropdown);
	};

	const handleTechChange = (tech: 'spatial' | 'ai' | 'agents') => {
		setActiveTech(tech);
	};

	// Technology details based on actual project stack
	const techDetails = {
		spatial: {
			title: 'PostGIS Spatial Database',
			subtitle: '752x faster than traditional APIs',
			description:
				'PostgreSQL with PostGIS extension powers our lightning-fast spatial queries. We store 46,591+ POIs with advanced geospatial indexing, delivering sub-second search results across millions of locations.',
			features: [
				'PostgreSQL + PostGIS for spatial operations',
				'46,591+ Points of Interest indexed',
				'Sub-0.1s query response time',
				'Advanced spatial indexing and optimization',
				'Geographic coverage: Istanbul, Turkey',
			],
			metrics: '< 0.053s response time',
			color: colors.brand.blue,
		},
		ai: {
			title: 'Multi-Agent LLM System',
			subtitle: 'Intelligent conversation orchestration',
			description:
				'Our FastAPI-powered LLM engine uses multiple specialized agents for location discovery, error handling, and natural language understanding. Built with OpenAI integration and smart context management.',
			features: [
				'FastAPI backend with Python',
				'OpenAI GPT integration for natural language',
				'Location Discovery Agent for smart search',
				'Error Handling Agent for robust responses',
				'Session management and conversation history',
			],
			metrics: 'Multi-agent orchestration',
			color: colors.brand.green,
		},
		agents: {
			title: 'Smart Task-Based Agents',
			subtitle: 'Specialized intelligence for every need',
			description:
				'Each agent specializes in specific tasks: location discovery, category classification, contextual advice, and error handling. This microservices architecture ensures reliable, fast, and intelligent responses.',
			features: [
				'Location Discovery Agent for POI search',
				'Category Classification for smart filtering',
				'Contextual Advice generation',
				'Unified error handling and status codes',
				'Clean microservices architecture',
			],
			metrics: 'Specialized task handling',
			color: colors.brand.navy,
		},
	};

	return (
		<div
			className='bg-transparent viewport-section relative'
			style={{
				minHeight: sectionMinHeight,
				paddingLeft: 0,
				paddingRight: 0,
				paddingTop: padding,
				paddingBottom: '120px',
			}}>
			{/* Background Elements */}
			<div className='absolute inset-0 opacity-5'>
				<div
					className='absolute top-1/4 left-1/4 w-32 h-32 animate-pulse'
					style={{
						background: `radial-gradient(circle, ${colors.brand.blue}40 0%, transparent 70%)`,
						animationDuration: '4s',
					}}
				/>
				<div
					className='absolute bottom-1/3 right-1/4 w-24 h-24 animate-pulse'
					style={{
						background: `linear-gradient(45deg, ${colors.brand.green}30 0%, transparent 70%)`,
						transform: 'rotate(45deg)',
						animationDuration: '3s',
						animationDelay: '1s',
					}}
				/>
			</div>

			<div className='relative z-10 w-full px-4 md:px-6 lg:px-12 max-w-7xl mx-auto'>
				{/* Section Header */}
				<div className='text-center mb-8 md:mb-12 lg:mb-16'>
					<div className='flex justify-center mb-4 md:mb-6'>
						<div
							className='inline-flex items-center space-x-2 px-4 py-2 md:px-6 md:py-3 border backdrop-blur-sm rounded-full'
							style={{
								background: `linear-gradient(135deg, ${colors.ui.blue50}80 0%, ${colors.ui.green50}80 100%)`,
								borderColor: colors.ui.gray200,
								boxShadow: '0 4px 16px rgba(51, 194, 255, 0.08)',
							}}>
							<FiCpu
								className='w-4 h-4 md:w-5 md:h-5'
								style={{ color: colors.brand.blue }}
							/>
							<span
								className='text-sm md:text-base font-medium'
								style={{ color: colors.neutral.textBlack }}>
								Advanced Technology
							</span>
						</div>
					</div>

					<h2
						className='text-2xl md:text-3xl lg:text-4xl font-bold mb-4 md:mb-6'
						style={{ color: colors.brand.navy }}>
						Cutting-edge tech that powers discovery
					</h2>

					<p
						className='text-base md:text-lg lg:text-xl max-w-3xl mx-auto leading-relaxed'
						style={{ color: colors.neutral.slateGray }}>
						Our platform combines cutting-edge spatial technology with
						intelligent algorithms for lightning-fast results.
					</p>
				</div>

				{/* Technology Buttons */}
				<div className='text-center mb-8 md:mb-12'>
					<div className='flex justify-center gap-3 md:gap-4 flex-wrap'>
						{(['spatial', 'ai', 'agents'] as const).map((tech) => (
							<button
								key={tech}
								onClick={() => {
									if (!showTechDropdown) {
										setShowTechDropdown(true);
									}
									handleTechChange(tech);
								}}
								className='px-4 py-2 md:px-6 md:py-3 font-medium transition-all duration-200 hover:scale-105 border-2'
								style={{
									background:
										showTechDropdown && activeTech === tech
											? techDetails[tech].color
											: 'transparent',
									color:
										showTechDropdown && activeTech === tech
											? 'white'
											: techDetails[tech].color,
									borderColor: techDetails[tech].color,
								}}>
								{tech === 'spatial' && (
									<FiZap className='w-4 h-4 md:w-5 md:h-5 inline mr-2' />
								)}
								{tech === 'ai' && (
									<FiTarget className='w-4 h-4 md:w-5 md:h-5 inline mr-2' />
								)}
								{tech === 'agents' && (
									<FiCpu className='w-4 h-4 md:w-5 md:h-5 inline mr-2' />
								)}
								{tech === 'spatial'
									? 'Spatial Database'
									: tech === 'ai'
									? 'LLM System'
									: 'Multi-Agent Architecture'}
							</button>
						))}
					</div>

					{/* Toggle Button */}
					<div className='flex justify-center mt-6'>
						<button
							onClick={handleTechToggle}
							className='px-6 py-3 font-medium transition-all duration-200 hover:scale-105 border-2'
							style={{
								background: showTechDropdown
									? colors.brand.navy
									: 'transparent',
								color: showTechDropdown ? 'white' : colors.brand.navy,
								borderColor: colors.brand.navy,
							}}>
							{showTechDropdown
								? '▼ Hide Technology Details'
								: '▶ Explore Our Technology'}
						</button>
					</div>
				</div>

				{/* Technology Dropdown Container */}
				{showTechDropdown && (
					<div
						className='w-full transition-all duration-500 ease-in-out'
						style={{
							opacity: showTechDropdown ? 1 : 0,
							transform: showTechDropdown
								? 'translateY(0)'
								: 'translateY(-20px)',
						}}>
						<div
							className='w-full max-w-5xl mx-auto bg-white/90 backdrop-blur-sm border border-gray-200/50 shadow-xl p-6 md:p-8 lg:p-10'
							style={{
								maxHeight: `calc(${viewportHeight * 0.95}px - 200px)`,
								minHeight: '400px',
								position: 'relative',
							}}>
							{/* Active Technology Content */}
							<div className='text-center mb-6'>
								<h3
									className='text-xl md:text-2xl lg:text-3xl font-bold mb-2'
									style={{ color: colors.brand.navy }}>
									{techDetails[activeTech].title}
								</h3>
								<p
									className='text-sm md:text-base font-medium mb-4'
									style={{ color: techDetails[activeTech].color }}>
									{techDetails[activeTech].subtitle}
								</p>
								<p
									className='text-base md:text-lg leading-relaxed mb-6'
									style={{ color: colors.neutral.slateGray }}>
									{techDetails[activeTech].description}
								</p>
							</div>

							{/* Features List */}
							<div className='grid grid-cols-1 md:grid-cols-2 gap-4 mb-6'>
								{techDetails[activeTech].features.map((feature, index) => (
									<div
										key={index}
										className='flex items-center space-x-3 p-3 border backdrop-blur-sm'
										style={{
											background: `linear-gradient(135deg, ${colors.neutral.cloudWhite}95 0%, ${colors.ui.blue50}30 100%)`,
											borderColor: colors.ui.gray200,
										}}>
										<div
											className='w-2 h-2 rounded-full flex-shrink-0'
											style={{ background: techDetails[activeTech].color }}
										/>
										<span
											className='text-sm md:text-base'
											style={{ color: colors.neutral.slateGray }}>
											{feature}
										</span>
									</div>
								))}
							</div>

							{/* Metrics */}
							<div className='text-center'>
								<div
									className='inline-block px-4 py-2 text-sm md:text-base font-medium rounded-full'
									style={{
										background: `${techDetails[activeTech].color}15`,
										color: techDetails[activeTech].color,
									}}>
									{techDetails[activeTech].metrics}
								</div>
							</div>
						</div>
					</div>
				)}
			</div>
		</div>
	);
};

export default TechnicalShowcase;
