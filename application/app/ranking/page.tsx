/** @format */

'use client';

import { colors } from '@/app/colors';
import {
	LocationSetup,
	useLocationManager,
	useLocationSetup,
} from '@/app/shared/locationManager';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { useCallback, useEffect, useRef, useState } from 'react';
import {
	FaComments,
	FaInfo,
	FaLocationArrow,
	FaMapMarkerAlt,
	FaTimes,
} from 'react-icons/fa';

import InfoPanel from './info-panel';
import Ranking, { RankingRef } from './ranking';
import { RankingNavContext } from './ranking-context';

// Global zoom constants - all functions use these values
const ZOOM_CONSTANTS = {
	INITIAL_ALTITUDE: 1.5,
	TRANSITION_THRESHOLD: 0.5, // Trigger redirect to flat map page
	MANUAL_RETURN_PROTECTION_THRESHOLD: 1.4, // Reset manual return protection at initial altitude
	SAFE_DISTANCE: 1.2, // Safe distance for manual return protection
} as const;

export default function RankingPage() {
	const router = useRouter();
	const rankingRef = useRef<RankingRef>(null);
	const [isClient, setIsClient] = useState(false);
	const [showInfo, setShowInfo] = useState(true);
	const [showSettings, setShowSettings] = useState(false);
	const [showNavButtons, setShowNavButtons] = useState(true);
	const [showBottomInstructions, setShowBottomInstructions] = useState(true);
	const [showLocationInfo, setShowLocationInfo] = useState(false);
	const [shouldFlashInfo, setShouldFlashInfo] = useState(false);
	const [flashCount, setFlashCount] = useState(0);
	const [showRankingTitle, setShowRankingTitle] = useState(true);

	const [currentLocation, setCurrentLocation] = useState<{
		lat: number;
		lng: number;
	} | null>(null);
	const [countriesData, setCountriesData] = useState<
		Array<Record<string, unknown>>
	>([]);
	const [userMarker, setUserMarker] = useState<Array<Record<string, unknown>>>(
		[]
	);
	const [currentZoom, setCurrentZoom] = useState<number>(
		ZOOM_CONSTANTS.INITIAL_ALTITUDE
	);
	const [isManuallyReturning, setIsManuallyReturning] = useState(false);
	const [manualReturnTime, setManualReturnTime] = useState<number>(0);
	const isManuallyReturningRef = useRef(false);
	const [isTransitioning, setIsTransitioning] = useState(false);
	const lastZoomChangeRef = useRef(Date.now());
	const [canAutoTransition, setCanAutoTransition] = useState(false);
	const hasMounted = useRef(false);
	const [userManuallyClickedInfo, setUserManuallyClickedInfo] = useState(false);

	// Use location manager hook
	const {
		location: userLocation,
		setupStatus,
		hasLoadedFromStorage,
	} = useLocationManager();

	// Integrate useLocationSetup
	const {
		showLocationSetup,
		isLocationSetupRequired,
		triggerLocationSetupIfNeeded,
		handleLocationSetupComplete,
	} = useLocationSetup();

	// Refs to store timer IDs for auto-hide
	const navButtonsTimerRef = useRef<NodeJS.Timeout | null>(null);
	const bottomInstructionsTimerRef = useRef<NodeJS.Timeout | null>(null);
	const rankingTitleTimerRef = useRef<NodeJS.Timeout | null>(null);

	// Update user marker and current location when location changes
	useEffect(() => {
		if (userLocation) {
			setUserMarker([
				{
					lat: userLocation.latitude,
					lng: userLocation.longitude,
					size: 1.2,
					color: '#FF0000', // Bright red color for maximum visibility
				},
			]);

			// Set current location to user location
			setCurrentLocation({
				lat: userLocation.latitude,
				lng: userLocation.longitude,
			});
		} else if (hasLoadedFromStorage) {
			// Only clear markers if we've loaded from storage and confirmed there's no location
			setUserMarker([]);
			setCurrentLocation(null);
		}
	}, [userLocation, hasLoadedFromStorage]);

	// Handle location setup completion - force location update
	useEffect(() => {
		if (setupStatus.hasSetup && userLocation) {
			// Force update the current location when setup is completed
			setCurrentLocation({
				lat: userLocation.latitude,
				lng: userLocation.longitude,
			});

			// Update user marker
			setUserMarker([
				{
					lat: userLocation.latitude,
					lng: userLocation.longitude,
					size: 1.2,
					color: '#FF0000',
				},
			]);

			// Also focus on the user location
			if (rankingRef.current) {
				setTimeout(() => {
					rankingRef.current?.pointOfView(
						{
							lat: userLocation.latitude,
							lng: userLocation.longitude,
							altitude: 2,
						},
						1500
					);
				}, 500); // Small delay to ensure ranking is ready
			}
		}
	}, [setupStatus.hasSetup, userLocation, setupStatus.setupTimestamp]);

	// Callback handlers for Globe component
	const handleZoomChange = (distance: number) => {
		console.log('📏 Zoom change:', {
			distance,
			isManuallyReturning,
			isTransitioning,
			isManuallyReturningRef: isManuallyReturningRef.current,
		});
		setCurrentZoom(distance);
		lastZoomChangeRef.current = Date.now();

		// Reset manual returning flag when user manually zooms out to a safe distance
		if (
			isManuallyReturningRef.current &&
			distance >= ZOOM_CONSTANTS.MANUAL_RETURN_PROTECTION_THRESHOLD
		) {
			console.log(
				'🔄 Manual return protection ended - user zoomed out to safe distance'
			);
			isManuallyReturningRef.current = false;
			setIsManuallyReturning(false);
			setManualReturnTime(0); // Reset the timestamp
			// Show a brief notification that auto-transition is available again
			setShowInfo(true);
			setUserManuallyClickedInfo(false);
			setTimeout(() => {
				setShowInfo(false);
			}, 2000);
		}
	};

	const handleLocationChange = (location: { lat: number; lng: number }) => {
		setCurrentLocation(location);
	};

	const handleLocationInfoToggle = () => {
		setShowLocationInfo(!showLocationInfo);
	};

	useEffect(() => {
		setIsClient(true);

		// Load countries data for borders - with fallback
		const loadCountriesData = async () => {
			try {
				// Try to fetch from external source first
				const response = await fetch(
					'https://raw.githubusercontent.com/holtzy/D3-graph-gallery/master/DATA/world.geojson'
				);
				if (response.ok) {
					const data = await response.json();
					setCountriesData(data.features);
					return;
				}
			} catch (error) {
				console.warn(
					'External countries data fetch failed, using fallback:',
					error
				);
			}

			// Fallback: Create a minimal world outline
			try {
				const fallbackData = {
					type: 'FeatureCollection',
					features: [
						{
							type: 'Feature',
							properties: { NAME: 'World', name: 'World' },
							geometry: {
								type: 'Polygon',
								coordinates: [
									[
										[-180, -90],
										[180, -90],
										[180, 90],
										[-180, 90],
										[-180, -90],
									],
								],
							},
						},
					],
				};
				setCountriesData(fallbackData.features);
				console.log('✅ Using fallback countries data');
			} catch (fallbackError) {
				console.error('Failed to load any countries data:', fallbackError);
				setCountriesData([]);
			}
		};

		loadCountriesData();
	}, []);

	// Auto-hide UI elements after 2 seconds and trigger flash
	useEffect(() => {
		if (showInfo && !userManuallyClickedInfo) {
			const timer = setTimeout(() => {
				setShowInfo(false);
				setShouldFlashInfo(true);
				setFlashCount((prev) => prev + 1);
			}, 2000);

			return () => clearTimeout(timer);
		}
	}, [showInfo, userManuallyClickedInfo]);

	// Auto-hide navigation buttons after 3 seconds
	useEffect(() => {
		if (showNavButtons) {
			const timer = setTimeout(() => {
				setShowNavButtons(false);
			}, 3000);

			return () => clearTimeout(timer);
		}
	}, [showNavButtons]);

	// Auto-hide bottom instructions after 5 seconds
	useEffect(() => {
		if (showBottomInstructions) {
			const timer = setTimeout(() => {
				setShowBottomInstructions(false);
			}, 5000);

			return () => clearTimeout(timer);
		}
	}, [showBottomInstructions]);

	// Auto-hide ranking title after 4 seconds
	useEffect(() => {
		if (showRankingTitle) {
			const timer = setTimeout(() => {
				setShowRankingTitle(false);
			}, 4000);

			return () => clearTimeout(timer);
		}
	}, [showRankingTitle]);

	// Flash info panel when needed
	useEffect(() => {
		if (shouldFlashInfo && flashCount < 3) {
			const flashTimer = setTimeout(() => {
				setShowInfo(true);
				setTimeout(() => {
					setShowInfo(false);
					setShouldFlashInfo(false);
				}, 1000);
			}, 2000);

			return () => clearTimeout(flashTimer);
		}
	}, [shouldFlashInfo, flashCount]);

	// Enable auto-transition after initial setup
	useEffect(() => {
		if (userLocation && !isLocationSetupRequired) {
			const timer = setTimeout(() => {
				setCanAutoTransition(true);
			}, 1000); // 1 second delay after location is set

			return () => clearTimeout(timer);
		}
	}, [userLocation, isLocationSetupRequired]);

	// Cleanup function for component unmount
	useEffect(() => {
		return () => {
			// Clear any remaining timers
			if (navButtonsTimerRef.current) {
				clearTimeout(navButtonsTimerRef.current);
			}
			if (bottomInstructionsTimerRef.current) {
				clearTimeout(bottomInstructionsTimerRef.current);
			}
			if (rankingTitleTimerRef.current) {
				clearTimeout(rankingTitleTimerRef.current);
			}
		};
	}, []);

	// Handle click outside for settings dropdown
	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			const target = event.target as Element;
			if (showSettings && !target.closest('.settings-dropdown')) {
				setShowSettings(false);
			}
		};

		document.addEventListener('mousedown', handleClickOutside);
		return () => document.removeEventListener('mousedown', handleClickOutside);
	}, [showSettings]);

	// Handle keyboard shortcuts
	useEffect(() => {
		const handleKeyDown = (e: KeyboardEvent) => {
			if (e.key === 'Escape') {
				// In globe view, go back to previous page
				router.back();
			}
		};

		document.addEventListener('keydown', handleKeyDown);
		return () => document.removeEventListener('keydown', handleKeyDown);
	}, [router]);

	const handleGoToUserLocation = useCallback(() => {
		if (userLocation) {
			// In ranking view, use the ranking's pointOfView method
			if (rankingRef.current) {
				rankingRef.current.pointOfView(
					{
						lat: userLocation.latitude,
						lng: userLocation.longitude,
						altitude: ZOOM_CONSTANTS.INITIAL_ALTITUDE,
					},
					1000
				);
			}
			setCurrentLocation({
				lat: userLocation.latitude,
				lng: userLocation.longitude,
			});
		}
	}, [userLocation]);

	const handleToggleGlobeInfo = useCallback(() => {
		if (showInfo) {
			// If toggling off, clear all timers and immediately hide all
			if (navButtonsTimerRef.current) clearTimeout(navButtonsTimerRef.current);
			if (bottomInstructionsTimerRef.current)
				clearTimeout(bottomInstructionsTimerRef.current);
			if (rankingTitleTimerRef.current)
				clearTimeout(rankingTitleTimerRef.current);
			setShowInfo(false);
			setShowNavButtons(false);
			setShowBottomInstructions(false);
			setShowRankingTitle(false);
			setShouldFlashInfo(false);
			setFlashCount(0);
			setUserManuallyClickedInfo(false);
		} else {
			setShowInfo(true);
			setShowNavButtons(true);
			setShowBottomInstructions(true);
			setShowRankingTitle(true);
			setShouldFlashInfo(false);
			setFlashCount(0);
			setUserManuallyClickedInfo(true);
		}
	}, [showInfo]);

	useEffect(() => {
		const timer = setTimeout(() => setCanAutoTransition(true), 3000);
		return () => clearTimeout(timer);
	}, []);

	// Auto-redirect to flat-map when zooming in close
	useEffect(() => {
		if (!hasMounted.current) {
			hasMounted.current = true;
			console.log('⏳ Skipping first redirect effect run');
			return;
		}
		if (isTransitioning || isManuallyReturning) return;

		const checkForRedirect = () => {
			const timeSinceLastChange = Date.now() - lastZoomChangeRef.current;

			// Only allow auto-redirect if canAutoTransition is true and user is zoomed in close
			if (
				canAutoTransition &&
				timeSinceLastChange >= 80 &&
				currentZoom < ZOOM_CONSTANTS.TRANSITION_THRESHOLD
			) {
				console.log('🌍 Redirecting to flat-map at distance:', currentZoom);

				// Clear interval immediately to prevent multiple triggers
				clearInterval(interval);

				// Redirect to flat-map page
				router.push('/flat-map');
				return;
			}
		};

		const interval = setInterval(checkForRedirect, 50); // Check every 50ms for faster response
		return () => clearInterval(interval);
	}, [
		currentZoom,
		isManuallyReturning,
		isTransitioning,
		canAutoTransition,
		router,
	]);

	// --- Stable handler refs for event listeners ---
	const goToUserLocationRef = useRef(handleGoToUserLocation);
	const toggleGlobeInfoRef = useRef(handleToggleGlobeInfo);
	useEffect(() => {
		goToUserLocationRef.current = handleGoToUserLocation;
		toggleGlobeInfoRef.current = handleToggleGlobeInfo;
	}, [handleGoToUserLocation, handleToggleGlobeInfo]);

	useEffect(() => {
		function handleGoToUserLocationEvent() {
			goToUserLocationRef.current();
		}
		function handleToggleGlobeInfoEvent() {
			toggleGlobeInfoRef.current();
		}
		window.addEventListener('goToUserLocation', handleGoToUserLocationEvent);
		window.addEventListener('toggleGlobeInfo', handleToggleGlobeInfoEvent);
		return () => {
			window.removeEventListener(
				'goToUserLocation',
				handleGoToUserLocationEvent
			);
			window.removeEventListener('toggleGlobeInfo', handleToggleGlobeInfoEvent);
		};
	}, []);

	// Place the prompt trigger in an effect
	useEffect(() => {
		triggerLocationSetupIfNeeded();
	}, [triggerLocationSetupIfNeeded]);

	// Provide focusOnUserLocation for nav bar and ranking view
	const focusOnUserLocation = useCallback(() => {
		if (userLocation && rankingRef.current) {
			rankingRef.current.pointOfView(
				{
					lat: userLocation.latitude,
					lng: userLocation.longitude,
					altitude: 2,
				},
				1500
			);
			setCurrentLocation({
				lat: userLocation.latitude,
				lng: userLocation.longitude,
			});
		}
	}, [userLocation]);

	return (
		<RankingNavContext.Provider value={{ focusOnUserLocation }}>
			<div className='min-h-screen w-full overflow-hidden'>
				{/* Floating Globe Controls (like map controls) */}
				<div
					className='fixed top-4 md:top-8 left-4 md:left-8 z-40 flex flex-col gap-2 md:gap-3 backdrop-blur-sm shadow-lg px-3 md:px-4 py-3 border items-center'
					style={{
						backgroundColor: 'rgba(255, 255, 255, 0.9)',
						borderColor: colors.ui.gray200,
					}}>
					{/* Logo to Home */}
					<button
						onClick={() => router.push('/')}
						className='p-2 transition-all duration-200 flex items-center justify-center w-10 h-10 border'
						title='Home'
						style={{
							backgroundColor: colors.ui.gray100,
							borderColor: colors.ui.gray200,
							boxShadow: `0 2px 8px ${colors.ui.gray200}40`,
						}}
						onMouseEnter={(e) => {
							e.currentTarget.style.backgroundColor = colors.ui.gray200;
							e.currentTarget.style.boxShadow = `0 4px 12px ${colors.ui.gray200}60`;
						}}
						onMouseLeave={(e) => {
							e.currentTarget.style.backgroundColor = colors.ui.gray100;
							e.currentTarget.style.boxShadow = `0 2px 8px ${colors.ui.gray200}40`;
						}}>
						<Image
							src='/logo/192x192.png'
							alt='Home'
							width={24}
							height={24}
							priority
						/>
					</button>
					{/* Existing Go to User Location button */}
					<button
						onClick={() => {
							if (userLocation) {
								focusOnUserLocation();
							} else {
								alert('No location available. Please set your location.');
							}
						}}
						className={`flex items-center justify-center w-10 h-10 transition-all duration-200 border ${
							!userLocation ? 'opacity-50 cursor-not-allowed' : ''
						}`}
						style={{
							backgroundColor: colors.ui.green100,
							color: colors.brand.green,
							borderColor: colors.ui.green200,
							boxShadow: `0 2px 8px ${colors.ui.green200}40`,
						}}
						title={
							userLocation ? 'Go to Your Location' : 'No location available'
						}
						onMouseEnter={(e) => {
							if (userLocation) {
								e.currentTarget.style.backgroundColor = colors.ui.green200;
								e.currentTarget.style.boxShadow = `0 4px 12px ${colors.ui.green200}60`;
							}
						}}
						onMouseLeave={(e) => {
							e.currentTarget.style.backgroundColor = colors.ui.green100;
							e.currentTarget.style.boxShadow = `0 2px 8px ${colors.ui.green200}40`;
						}}>
						<FaLocationArrow className='w-4 h-4' />
					</button>

					{/* Chat Page button */}
					<button
						onClick={() => router.push('/chat')}
						className='flex items-center justify-center w-10 h-10 transition-all duration-200 border'
						style={{
							backgroundColor: colors.ui.blue100,
							color: colors.brand.blue,
							borderColor: colors.ui.blue200,
							boxShadow: `0 2px 8px ${colors.ui.blue200}40`,
						}}
						title='Chat'
						onMouseEnter={(e) => {
							e.currentTarget.style.backgroundColor = colors.ui.blue200;
							e.currentTarget.style.boxShadow = `0 4px 12px ${colors.ui.blue200}60`;
						}}
						onMouseLeave={(e) => {
							e.currentTarget.style.backgroundColor = colors.ui.blue100;
							e.currentTarget.style.boxShadow = `0 2px 8px ${colors.ui.blue200}40`;
						}}>
						<FaComments className='w-4 h-4' />
					</button>

					{/* POI Page button */}
					<button
						onClick={() => router.push('/pois')}
						className='flex items-center justify-center w-10 h-10 transition-all duration-200 border'
						style={{
							backgroundColor: colors.ui.gray100,
							color: colors.supporting.teal,
							borderColor: colors.ui.gray200,
							boxShadow: `0 2px 8px ${colors.ui.gray200}40`,
						}}
						title='Places of Interest'
						onMouseEnter={(e) => {
							e.currentTarget.style.backgroundColor = colors.ui.gray200;
							e.currentTarget.style.boxShadow = `0 4px 12px ${colors.ui.gray200}60`;
						}}
						onMouseLeave={(e) => {
							e.currentTarget.style.backgroundColor = colors.ui.gray100;
							e.currentTarget.style.boxShadow = `0 2px 8px ${colors.ui.gray200}40`;
						}}>
						<FaMapMarkerAlt className='w-4 h-4' />
					</button>
				</div>
				{/* Interactive Ranking */}
				<Ranking
					ref={rankingRef}
					isClient={isClient}
					countriesData={countriesData}
					userMarker={userMarker}
					currentZoom={currentZoom}
					currentLocation={currentLocation}
					userLocation={
						userLocation
							? {
									lat: userLocation.latitude,
									lng: userLocation.longitude,
							  }
							: null
					}
					onZoomChange={handleZoomChange}
					onLocationChange={handleLocationChange}
					onLocationInfoToggle={handleLocationInfoToggle}
					isManuallyReturning={isManuallyReturning}
					isTransitioning={isTransitioning}
					showNavButtons={showNavButtons}
					manualReturnTime={manualReturnTime}
					isManuallyReturningRef={isManuallyReturningRef}
				/>

				{/* Info Panel */}
				<InfoPanel
					showInfo={showInfo}
					setShowInfo={setShowInfo}
				/>

				{/* Ranking Title - Bottom Center */}
				{showRankingTitle && (
					<div className='absolute bottom-6 left-1/2 transform -translate-x-1/2 z-20'>
						<div className='bg-white/90 backdrop-blur-sm px-6 py-3 shadow-lg border border-gray-200'>
							<div className='flex items-center space-x-3'>
								<div className='w-3 h-3 bg-green-500 rounded-full animate-pulse'></div>
								<span className='text-slate-700 font-medium'>
									Interactive Ranking - Global Explorer
								</span>
							</div>
						</div>
					</div>
				)}

				{/* Bottom Instructions */}
				{showBottomInstructions && (
					<div className='absolute bottom-20 left-1/2 transform -translate-x-1/2 z-20'>
						<div className='bg-white/90 backdrop-blur-sm px-6 py-3 shadow-lg border border-gray-200'>
							<div className='flex items-center gap-4'>
								<p className='text-slate-700 text-sm font-medium'>
									🌍 Explore the World • 🔍 Zoom close for flat map • 📍
									Discover locations
									{isManuallyReturning && (
										<span className='ml-2 text-orange-600'>
											🛡️ Auto-transition protected
										</span>
									)}
								</p>
							</div>
						</div>
					</div>
				)}

				{/* Location Info Panel - Only show when clicked */}
				{showLocationInfo && userLocation && (
					<div className='absolute bottom-20 left-6 z-20 animate-slide-down'>
						<div className='bg-white/95 backdrop-blur-sm rounded-xl p-4 shadow-lg max-w-xs'>
							<div className='text-sm text-slate-600'>
								<div className='flex items-center justify-between mb-3'>
									<div className='font-medium text-slate-800'>
										Your Location
									</div>
									<button
										onClick={() => setShowLocationInfo(false)}
										className='text-slate-500 hover:text-slate-700 transition-colors'>
										<FaTimes className='w-3 h-3' />
									</button>
								</div>

								<div className='mb-2 p-2 bg-red-50 rounded-lg'>
									<div className='font-medium text-red-800 text-xs mb-1'>
										📍 Coordinates
									</div>
									<div className='text-red-700'>
										{userLocation.latitude.toFixed(4)},{' '}
										{userLocation.longitude.toFixed(4)}
									</div>
								</div>

								<div className='space-y-1 text-xs'>
									<div>
										🎯 Accuracy: ±{Math.round(userLocation.accuracy || 0)}m
									</div>
									<div>
										⏰ Updated:{' '}
										{Math.floor((Date.now() - userLocation.timestamp) / 1000) <
										60
											? 'just now'
											: `${Math.floor(
													(Date.now() - userLocation.timestamp) / 60000
											  )}m ago`}
									</div>
								</div>
							</div>
						</div>
					</div>
				)}

				{/* Info Button at Bottom */}
				<div className='fixed bottom-4 md:bottom-8 left-1/2 transform -translate-x-1/2 z-40'>
					<button
						onClick={handleToggleGlobeInfo}
						className='flex items-center justify-center w-12 h-12 md:w-14 md:h-14 transition-all duration-200 border shadow-lg'
						style={{
							backgroundColor: showInfo ? colors.ui.blue200 : colors.ui.blue100,
							color: colors.brand.blue,
							borderColor: colors.ui.blue200,
							boxShadow: `0 4px 16px ${colors.ui.blue200}60`,
						}}
						title='Info & Navigation'
						onMouseEnter={(e) => {
							e.currentTarget.style.backgroundColor = colors.ui.blue200;
							e.currentTarget.style.boxShadow = `0 6px 20px ${colors.ui.blue200}80`;
							e.currentTarget.style.transform = 'scale(1.05)';
						}}
						onMouseLeave={(e) => {
							e.currentTarget.style.backgroundColor = showInfo
								? colors.ui.blue200
								: colors.ui.blue100;
							e.currentTarget.style.boxShadow = `0 4px 16px ${colors.ui.blue200}60`;
							e.currentTarget.style.transform = 'scale(1)';
						}}>
						<FaInfo className='w-5 h-5 md:w-6 md:h-6' />
					</button>
				</div>

				{/* Location Setup Modal/Overlay */}
				<LocationSetup
					isOpen={showLocationSetup}
					onComplete={handleLocationSetupComplete}
					pageContext='globe'
					isModal={false}
					setCurrentLocation={setCurrentLocation}
					title='Location Setup Required'
					subtitle='To explore the interactive globe, we need to know your location or you can set it manually.'
				/>
			</div>
		</RankingNavContext.Provider>
	);
}
