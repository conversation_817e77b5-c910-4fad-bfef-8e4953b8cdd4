/** @format */

'use client';

import { colors } from '@/app/colors';
import React, { useState } from 'react';

interface Category {
	id: string;
	emoji: string;
	name: string;
	count: number;
	places: string[];
}

const ExploreDemo: React.FC = () => {
	const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

	const categories: Category[] = [
		{
			id: 'food',
			emoji: '🍽️',
			name: 'Food & Drink',
			count: 2847,
			places: [
				'Starbucks Coffee',
				'Local Bistro',
				'Pizza Palace',
				'Sushi Master',
				'Turkish Delight Cafe',
			],
		},
		{
			id: 'culture',
			emoji: '🎭',
			name: 'Culture',
			count: 1203,
			places: [
				'Art Gallery Modern',
				'History Museum',
				'Cultural Center',
				'Theater District',
				'Music Hall',
			],
		},
		{
			id: 'wellness',
			emoji: '🧘',
			name: 'Wellness',
			count: 856,
			places: [
				'Zen Yoga Studio',
				'Spa Retreat',
				'Meditation Center',
				'Fitness Club',
				'Wellness Clinic',
			],
		},
		{
			id: 'sports',
			emoji: '⚽',
			name: 'Sports',
			count: 1429,
			places: [
				'Sports Complex',
				'Tennis Club',
				'Swimming Pool',
				'Basketball Court',
				'Football Stadium',
			],
		},
		{
			id: 'shopping',
			emoji: '🛍️',
			name: 'Shopping',
			count: 1654,
			places: [
				'Grand Mall',
				'Local Market',
				'Fashion Boutique',
				'Electronics Store',
				'Bookstore',
			],
		},
		{
			id: 'nature',
			emoji: '🌳',
			name: 'Nature',
			count: 743,
			places: [
				'Central Park',
				'Botanical Garden',
				'Nature Trail',
				'Lake View',
				'Forest Reserve',
			],
		},
	];

	const handleCategoryClick = (categoryId: string) => {
		setSelectedCategory(selectedCategory === categoryId ? null : categoryId);
	};

	const selectedCategoryData = categories.find(
		(cat) => cat.id === selectedCategory
	);

	return (
		<div
			className='p-6 md:p-8 border backdrop-blur-sm'
			style={{
				background: `linear-gradient(135deg, ${colors.brand.accent}10 0%, ${colors.neutral.cloudWhite}90 100%)`,
				borderColor: colors.brand.accent,
				boxShadow: `0 12px 40px ${colors.brand.accent}20`,
			}}>
			<div className='text-center mb-6'>
				<h3
					className='text-xl md:text-2xl font-bold mb-2'
					style={{ color: colors.brand.accent }}>
					🧭 Live Explore Demo
				</h3>
				<p
					className='text-sm md:text-base'
					style={{ color: colors.neutral.slateGray }}>
					Click on any category to see real places near you
				</p>
			</div>

			{/* Interactive Category Grid */}
			<div className='grid grid-cols-2 md:grid-cols-3 gap-4 max-w-4xl mx-auto mb-6'>
				{categories.map((category) => (
					<button
						key={category.id}
						onClick={() => handleCategoryClick(category.id)}
						className={`bg-white p-4 text-center hover:shadow-lg transition-all duration-200 cursor-pointer transform hover:scale-105 ${
							selectedCategory === category.id ? 'shadow-lg scale-105' : ''
						}`}
						style={{
							border:
								selectedCategory === category.id
									? `2px solid ${colors.brand.accent}`
									: '1px solid #e5e7eb',
						}}>
						<div className='text-2xl mb-2'>{category.emoji}</div>
						<div className='font-medium text-sm mb-1'>{category.name}</div>
						<div className='text-xs text-gray-500'>
							{category.count.toLocaleString()} places
						</div>
						{selectedCategory === category.id && (
							<div className='mt-2 text-xs text-green-600 font-medium'>
								▼ Click to see places
							</div>
						)}
					</button>
				))}
			</div>

			{/* Selected Category Results */}
			{selectedCategoryData && (
				<div className='bg-white rounded-lg p-4 shadow-inner max-w-4xl mx-auto'>
					<div className='text-center mb-4'>
						<h4 className='font-bold text-lg mb-2'>
							{selectedCategoryData.emoji} {selectedCategoryData.name} Near You
						</h4>
						<p className='text-sm text-gray-600'>
							Showing top 5 of {selectedCategoryData.count.toLocaleString()}{' '}
							places
						</p>
					</div>

					<div className='grid grid-cols-1 md:grid-cols-2 gap-3'>
						{selectedCategoryData.places.map((place, index) => (
							<div
								key={index}
								className='flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer'>
								<div className='flex items-center gap-3'>
									<span className='font-bold text-green-500'>#{index + 1}</span>
									<div>
										<div className='font-medium text-sm'>{place}</div>
										<div className='text-xs text-gray-500'>
											{(Math.random() * 2 + 0.1).toFixed(1)}km •{' '}
											{(Math.random() * 1.5 + 4).toFixed(1)}⭐
										</div>
									</div>
								</div>
								<button
									onClick={() => (window.location.href = '/explore')}
									className='text-xs px-2 py-1 text-white rounded hover:opacity-90 transition-colors'
									style={{ backgroundColor: colors.brand.accent }}>
									View
								</button>
							</div>
						))}
					</div>

					<div className='text-center mt-4'>
						<button
							onClick={() => (window.location.href = '/explore')}
							className='px-4 py-2 text-white rounded-lg hover:opacity-90 transition-colors'
							style={{ backgroundColor: colors.brand.accent }}>
							Explore All {selectedCategoryData.name} →
						</button>
					</div>
				</div>
			)}

			{!selectedCategory && (
				<div className='text-center'>
					<p className='text-sm text-gray-600 mb-4'>
						👆 Click any category above to see live results
					</p>
					<button
						onClick={() => (window.location.href = '/explore')}
						className='px-4 py-2 text-white rounded-lg hover:opacity-90 transition-colors'
						style={{ backgroundColor: colors.brand.accent }}>
						Go to Full Explore Page →
					</button>
				</div>
			)}
		</div>
	);
};

export default ExploreDemo;
