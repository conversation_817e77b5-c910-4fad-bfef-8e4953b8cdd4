/** @format */

'use client';

import { colors } from '@/app/colors';
import React, { useEffect, useState } from 'react';
import {
	FaCheck,
	FaChevronDown,
	FaFilter,
	FaMapMarkerAlt,
	FaSearch,
	FaTimes,
} from 'react-icons/fa';

interface POICategory {
	subcategory: string;
	count: number;
}

interface POIFilterGroup {
	group: string;
	subcategories: POICategory[];
}

interface POIFilterProps {
	isOpen: boolean;
	onClose: () => void;
	selectedCategories: string[];
	onCategoriesChange: (categories: string[]) => void;
	selectedSubcategories: string[];
	onSubcategoriesChange: (subcategories: string[]) => void;
	onApplyFilters: () => void;
	userLocation?: { latitude: number; longitude: number } | null;
	city: string[];
	onCityChange: (city: string[]) => void;
	district: string[];
	onDistrictChange: (district: string[]) => void;
	category: string[];
	onCategoryChange: (category: string[]) => void;
	subcategory: string[];
	onSubcategoryChange: (subcategory: string[]) => void;
	cuisine: string[];
	onCuisineChange: (cuisine: string[]) => void;
}

const POIFilter: React.FC<POIFilterProps> = ({
	isOpen,
	onClose,
	selectedCategories,
	onCategoriesChange,
	selectedSubcategories,
	onSubcategoriesChange,
	onApplyFilters,
	userLocation,
	city,
	onCityChange,
	district,
	onDistrictChange,
	category,
	onCategoryChange,
	subcategory,
	onSubcategoryChange,
	cuisine,
	onCuisineChange,
}) => {
	const [categories, setCategories] = useState<POICategory[]>([]);
	const [groupedCategories, setGroupedCategories] = useState<POIFilterGroup[]>(
		[]
	);
	const [loading, setLoading] = useState(false);
	const [searchTerm, setSearchTerm] = useState('');
	const [expandedGroups, setExpandedGroups] = useState<Set<string>>(
		new Set(['food_drink', 'shopping'])
	);
	const [expandedFilters, setExpandedFilters] = useState<Set<string>>(
		new Set(['location'])
	);

	// Dropdown states
	const [dropdownOptions, setDropdownOptions] = useState({
		cities: [] as string[],
		districts: [] as string[],
		categories: [] as string[],
		subcategories: [] as string[],
		cuisines: [] as string[],
	});
	const [openDropdowns, setOpenDropdowns] = useState<Set<string>>(new Set());
	const [dropdownSearchTerms, setDropdownSearchTerms] = useState({
		city: '',
		district: '',
		category: '',
		subcategory: '',
		cuisine: '',
	});

	// Load POI categories when component mounts
	useEffect(() => {
		if (isOpen) {
			loadCategories();
			loadDropdownOptions();
		}
	}, [isOpen]);

	const loadCategories = async () => {
		setLoading(true);
		try {
			const response = await fetch('/api/pois/categories');
			const data = await response.json();

			if (data.success) {
				setCategories(data.categories || []);
				setGroupedCategories(data.grouped || []);
			}
		} catch (error) {
			console.error('Failed to load POI categories:', error);
		} finally {
			setLoading(false);
		}
	};

	const loadDropdownOptions = async () => {
		try {
			// Load unique values for each filter type
			const response = await fetch('/api/pois/filter-options');
			const data = await response.json();

			if (data.success) {
				setDropdownOptions({
					cities: data.cities || [],
					districts: data.districts || [],
					categories: data.categories || [],
					subcategories: data.subcategories || [],
					cuisines: data.cuisines || [],
				});
			}
		} catch (error) {
			console.error('Failed to load dropdown options:', error);
		}
	};

	const toggleSubcategory = (subcategory: string) => {
		const newSubcategories = selectedSubcategories.includes(subcategory)
			? selectedSubcategories.filter((s) => s !== subcategory)
			: [...selectedSubcategories, subcategory];

		onSubcategoriesChange(newSubcategories);
	};

	const toggleGroup = (groupName: string) => {
		const newExpanded = new Set(expandedGroups);
		if (newExpanded.has(groupName)) {
			newExpanded.delete(groupName);
		} else {
			newExpanded.add(groupName);
		}
		setExpandedGroups(newExpanded);
	};

	const toggleFilterSection = (sectionName: string) => {
		const newExpanded = new Set(expandedFilters);
		if (newExpanded.has(sectionName)) {
			newExpanded.delete(sectionName);
		} else {
			newExpanded.add(sectionName);
		}
		setExpandedFilters(newExpanded);
	};

	const clearAllFilters = () => {
		onCategoriesChange([]);
		onSubcategoriesChange([]);
		onCityChange([]);
		onDistrictChange([]);
		onCategoryChange([]);
		onSubcategoryChange([]);
		onCuisineChange([]);
	};

	const selectAllInGroup = (group: POIFilterGroup) => {
		const groupSubcategories = (group.subcategories || []).map(
			(s) => s.subcategory
		);
		const newSubcategories = Array.from(
			new Set([...selectedSubcategories, ...groupSubcategories])
		);
		onSubcategoriesChange(newSubcategories);
	};

	const getGroupIcon = (groupName: string) => {
		const icons: { [key: string]: string } = {
			food_drink: '🍽️',
			shopping: '🛍️',
			services: '🏦',
			healthcare: '🏥',
			education: '🎓',
			accommodation: '🏨',
			transport: '🚗',
			recreation: '🎮',
			worship: '⛪',
		};
		return icons[groupName] || '📍';
	};

	const getGroupDisplayName = (groupName: string) => {
		const names: { [key: string]: string } = {
			food_drink: 'Food & Drink',
			shopping: 'Shopping',
			services: 'Services',
			healthcare: 'Healthcare',
			education: 'Education',
			accommodation: 'Accommodation',
			transport: 'Transport',
			recreation: 'Recreation',
			worship: 'Worship',
		};
		return names[groupName] || groupName;
	};

	// Multi-select dropdown handlers
	const handleMultiSelect = (type: string, value: string) => {
		const currentValues = getCurrentValues(type);
		const newValues = currentValues.includes(value)
			? currentValues.filter((v) => v !== value)
			: [...currentValues, value];

		updateValues(type, newValues);
	};

	const getCurrentValues = (type: string): string[] => {
		switch (type) {
			case 'city':
				return city;
			case 'district':
				return district;
			case 'category':
				return category;
			case 'subcategory':
				return subcategory;
			case 'cuisine':
				return cuisine;
			default:
				return [];
		}
	};

	const updateValues = (type: string, values: string[]) => {
		switch (type) {
			case 'city':
				onCityChange(values);
				break;
			case 'district':
				onDistrictChange(values);
				break;
			case 'category':
				onCategoryChange(values);
				break;
			case 'subcategory':
				onSubcategoryChange(values);
				break;
			case 'cuisine':
				onCuisineChange(values);
				break;
		}
	};

	const getFilteredOptions = (type: string): string[] => {
		const options = dropdownOptions[type as keyof typeof dropdownOptions] || [];
		const searchTerm =
			dropdownSearchTerms[type as keyof typeof dropdownSearchTerms];
		return options.filter((option) =>
			option.toLowerCase().includes(searchTerm.toLowerCase())
		);
	};

	const MultiSelectDropdown = ({
		type,
		label,
		placeholder,
	}: {
		type: string;
		label: string;
		placeholder: string;
	}) => {
		const isOpen = openDropdowns.has(type);
		const currentValues = getCurrentValues(type);
		const filteredOptions = getFilteredOptions(type);
		const searchTerm =
			dropdownSearchTerms[type as keyof typeof dropdownSearchTerms];

		return (
			<div className='relative'>
				<label
					className='block text-sm font-medium mb-2'
					style={{ color: colors.neutral.textBlack }}>
					{label}
				</label>
				<div className='relative'>
					<input
						type='text'
						value={searchTerm}
						onChange={(e) =>
							setDropdownSearchTerms((prev) => ({
								...prev,
								[type]: e.target.value,
							}))
						}
						onFocus={() => setOpenDropdowns(new Set([...openDropdowns, type]))}
						placeholder={placeholder}
						className='w-full px-3 py-2 border focus:outline-none focus:ring-2'
						style={{ borderColor: colors.ui.gray300 }}
					/>
					<FaChevronDown
						className={`absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 transition-transform ${
							isOpen ? 'rotate-180' : ''
						}`}
						style={{ color: colors.neutral.slateGray }}
					/>
				</div>

				{isOpen && (
					<div className='absolute z-10 w-full mt-1 bg-white border shadow-lg max-h-48 overflow-y-auto'>
						{filteredOptions.length === 0 ? (
							<div className='px-3 py-2 text-sm text-gray-500'>
								No options found
							</div>
						) : (
							filteredOptions.map((option) => (
								<div
									key={option}
									onClick={() => handleMultiSelect(type, option)}
									className='flex items-center justify-between px-3 py-2 hover:bg-gray-50 cursor-pointer'>
									<span className='text-sm'>{option}</span>
									{currentValues.includes(option) && (
										<FaCheck
											className='w-3 h-3'
											style={{ color: colors.brand.blue }}
										/>
									)}
								</div>
							))
						)}
					</div>
				)}

				{/* Selected values display */}
				{currentValues.length > 0 && (
					<div className='flex flex-wrap gap-1 mt-2'>
						{currentValues.map((value) => (
							<span
								key={value}
								className='inline-flex items-center gap-1 px-2 py-1 text-xs bg-blue-100 text-blue-800'>
								{value}
								<button
									onClick={() => handleMultiSelect(type, value)}
									className='hover:bg-blue-200 p-0.5'>
									×
								</button>
							</span>
						))}
					</div>
				)}
			</div>
		);
	};

	// Filter categories based on search term
	const filteredCategories = categories.filter(
		(cat) =>
			cat.subcategory &&
			cat.subcategory.toLowerCase().includes(searchTerm.toLowerCase())
	);

	if (!isOpen) return null;

	return (
		<div className='fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm p-4'>
			<div
				className='bg-white shadow-2xl max-w-4xl w-full max-h-[90vh] flex flex-col border'
				style={{ borderColor: colors.ui.gray200 }}>
				{/* Header */}
				<div
					className='flex items-center justify-between p-6 border-b flex-shrink-0'
					style={{ borderColor: colors.ui.gray200 }}>
					<div className='flex items-center gap-3'>
						<FaFilter style={{ color: colors.brand.blue }} />
						<h2
							className='text-xl font-bold'
							style={{ color: colors.neutral.textBlack }}>
							Advanced Filters
						</h2>
					</div>
					<button
						onClick={onClose}
						className='p-2 hover:bg-gray-100 transition-colors border border-gray-200'
						style={{ color: colors.neutral.slateGray }}>
						<FaTimes className='w-5 h-5' />
					</button>
				</div>

				{/* Search */}
				<div
					className='p-6 border-b flex-shrink-0'
					style={{ borderColor: colors.ui.gray200 }}>
					<div className='relative'>
						<FaSearch
							className='absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4'
							style={{ color: colors.neutral.slateGray }}
						/>
						<input
							type='text'
							placeholder='Search categories...'
							value={searchTerm}
							onChange={(e) => setSearchTerm(e.target.value)}
							className='w-full pl-10 pr-4 py-3 border focus:outline-none focus:ring-2'
							style={{
								borderColor: colors.ui.gray300,
							}}
						/>
					</div>
				</div>

				{/* Filter Controls */}
				<div
					className='p-6 border-b flex-shrink-0'
					style={{ borderColor: colors.ui.gray200 }}>
					<div className='flex items-center justify-between mb-4'>
						<span
							className='text-sm font-medium'
							style={{ color: colors.neutral.textBlack }}>
							Selected:{' '}
							{selectedCategories.length + selectedSubcategories.length} filters
						</span>
						<div className='flex gap-2'>
							<button
								onClick={clearAllFilters}
								className='text-sm px-3 py-1 border transition-colors'
								style={{
									borderColor: colors.ui.gray300,
									color: colors.neutral.slateGray,
								}}>
								Clear All
							</button>
							<button
								onClick={onApplyFilters}
								className='text-sm px-4 py-2 text-white font-medium transition-colors'
								style={{ backgroundColor: colors.brand.blue }}>
								Apply Filters
							</button>
						</div>
					</div>
				</div>

				{/* Content - Scrollable */}
				<div className='flex-1 overflow-y-auto p-6'>
					<div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
						{/* Location Filters */}
						<div className='space-y-4'>
							<div
								className='border rounded-xl overflow-hidden'
								style={{ borderColor: colors.ui.gray200 }}>
								<button
									onClick={() => toggleFilterSection('location')}
									className='w-full flex items-center justify-between p-4 hover:bg-gray-50 transition-colors'>
									<div className='flex items-center gap-3'>
										<FaMapMarkerAlt style={{ color: colors.brand.blue }} />
										<span
											className='font-medium'
											style={{ color: colors.neutral.textBlack }}>
											Location Filters
										</span>
									</div>
									<FaChevronDown
										className={`transform transition-transform ${
											expandedFilters.has('location') ? 'rotate-180' : ''
										}`}
										style={{ color: colors.neutral.slateGray }}
									/>
								</button>

								{expandedFilters.has('location') && (
									<div
										className='border-t p-4 space-y-4'
										style={{ borderColor: colors.ui.gray200 }}>
										<MultiSelectDropdown
											type='city'
											label='City'
											placeholder='Search or type city name'
										/>
										<MultiSelectDropdown
											type='district'
											label='District'
											placeholder='Search or type district name'
										/>
									</div>
								)}
							</div>

							{/* Category Filters */}
							<div
								className='border overflow-hidden'
								style={{ borderColor: colors.ui.gray200 }}>
								<button
									onClick={() => toggleFilterSection('category')}
									className='w-full flex items-center justify-between p-4 hover:bg-gray-50 transition-colors'>
									<div className='flex items-center gap-3'>
										<span className='text-xl'>📂</span>
										<span
											className='font-medium'
											style={{ color: colors.neutral.textBlack }}>
											Category Filters
										</span>
									</div>
									<FaChevronDown
										className={`transform transition-transform ${
											expandedFilters.has('category') ? 'rotate-180' : ''
										}`}
										style={{ color: colors.neutral.slateGray }}
									/>
								</button>

								{expandedFilters.has('category') && (
									<div
										className='border-t p-4 space-y-4'
										style={{ borderColor: colors.ui.gray200 }}>
										<MultiSelectDropdown
											type='category'
											label='Category'
											placeholder='Search or type category'
										/>
										<MultiSelectDropdown
											type='subcategory'
											label='Subcategory'
											placeholder='Search or type subcategory'
										/>
										<MultiSelectDropdown
											type='cuisine'
											label='Cuisine'
											placeholder='Search or type cuisine type'
										/>
									</div>
								)}
							</div>
						</div>

						{/* Category Types */}
						<div className='space-y-4'>
							<h3
								className='font-medium'
								style={{ color: colors.neutral.textBlack }}>
								Category Types
							</h3>
							{loading ? (
								<div className='text-center py-8'>
									<div
										className='animate-spin h-8 w-8 border-b-2 mx-auto mb-4'
										style={{ borderColor: colors.brand.blue }}></div>
									<p style={{ color: colors.neutral.slateGray }}>
										Loading categories...
									</p>
								</div>
							) : searchTerm ? (
								// Show filtered individual categories when searching
								<div className='space-y-2'>
									{(filteredCategories || []).map((category) => (
										<label
											key={category.subcategory}
											className='flex items-center justify-between p-3 border cursor-pointer hover:bg-gray-50 transition-colors'
											style={{ borderColor: colors.ui.gray200 }}>
											<div className='flex items-center gap-3'>
												<input
													type='checkbox'
													checked={selectedSubcategories.includes(
														category.subcategory
													)}
													onChange={() =>
														toggleSubcategory(category.subcategory)
													}
													className='w-4 h-4 rounded'
													style={{ accentColor: colors.brand.blue }}
												/>
												<span
													className='font-medium capitalize'
													style={{ color: colors.neutral.textBlack }}>
													{category.subcategory.replace(/_/g, ' ')}
												</span>
											</div>
											<span
												className='text-sm px-2 py-1 bg-gray-100'
												style={{ color: colors.neutral.slateGray }}>
												{category.count}
											</span>
										</label>
									))}
								</div>
							) : (
								// Show grouped categories
								<div className='space-y-4'>
									{(groupedCategories || []).map((group) => (
										<div
											key={group.group}
											className='border overflow-hidden'
											style={{ borderColor: colors.ui.gray200 }}>
											<button
												onClick={() => toggleGroup(group.group)}
												className='w-full flex items-center justify-between p-4 hover:bg-gray-50 transition-colors'>
												<div className='flex items-center gap-3'>
													<span className='text-xl'>
														{getGroupIcon(group.group)}
													</span>
													<span
														className='font-medium'
														style={{ color: colors.neutral.textBlack }}>
														{getGroupDisplayName(group.group)}
													</span>
													<span
														className='text-sm px-2 py-1 bg-gray-100'
														style={{ color: colors.neutral.slateGray }}>
														{group.subcategories.length}
													</span>
												</div>
												<div className='flex items-center gap-2'>
													<div
														onClick={(e) => {
															e.stopPropagation();
															selectAllInGroup(group);
														}}
														className='text-xs px-2 py-1 border cursor-pointer'
														style={{
															borderColor: colors.brand.blue,
															color: colors.brand.blue,
														}}>
														Select All
													</div>
													<span
														className={`transform transition-transform ${
															expandedGroups.has(group.group)
																? 'rotate-180'
																: ''
														}`}>
														▼
													</span>
												</div>
											</button>

											{expandedGroups.has(group.group) && (
												<div
													className='border-t p-4 space-y-2'
													style={{ borderColor: colors.ui.gray200 }}>
													{(group.subcategories || []).map((subcategory) => (
														<label
															key={subcategory.subcategory}
															className='flex items-center justify-between p-2 cursor-pointer hover:bg-gray-50 transition-colors'>
															<div className='flex items-center gap-3'>
																<input
																	type='checkbox'
																	checked={selectedSubcategories.includes(
																		subcategory.subcategory
																	)}
																	onChange={() =>
																		toggleSubcategory(subcategory.subcategory)
																	}
																	className='w-4 h-4'
																	style={{ accentColor: colors.brand.blue }}
																/>
																<span
																	className='capitalize'
																	style={{ color: colors.neutral.textBlack }}>
																	{subcategory.subcategory.replace(/_/g, ' ')}
																</span>
															</div>
															<span
																className='text-sm px-2 py-1 bg-gray-100'
																style={{ color: colors.neutral.slateGray }}>
																{subcategory.count}
															</span>
														</label>
													))}
												</div>
											)}
										</div>
									))}
								</div>
							)}
						</div>
					</div>
				</div>

				{/* Footer */}
				<div
					className='flex items-center justify-between p-6 border-t bg-gray-50 flex-shrink-0'
					style={{ borderColor: colors.ui.gray200 }}>
					<div
						className='text-sm'
						style={{ color: colors.neutral.slateGray }}>
						{userLocation ? (
							<span className='flex items-center gap-2'>
								<FaMapMarkerAlt style={{ color: colors.brand.blue }} />
								Filters will apply to your location area
							</span>
						) : (
							<span>Set your location for better filtering</span>
						)}
					</div>
					<div className='flex gap-3'>
						<button
							onClick={onClose}
							className='px-4 py-2 border transition-colors'
							style={{
								borderColor: colors.ui.gray300,
								color: colors.neutral.slateGray,
							}}>
							Cancel
						</button>
						<button
							onClick={() => {
								onApplyFilters();
								onClose();
							}}
							className='px-6 py-2 text-white font-medium transition-colors'
							style={{ backgroundColor: colors.brand.blue }}>
							Apply Filters (
							{selectedCategories.length + selectedSubcategories.length})
						</button>
					</div>
				</div>
			</div>
		</div>
	);
};

export default POIFilter;
