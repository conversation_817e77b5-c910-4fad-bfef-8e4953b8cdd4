/** @format */

'use client';

import { colors } from '@/app/colors';
import React, { useState } from 'react';

interface RankingItem {
	rank: number;
	name: string;
	score: string;
	type: string;
	distance: string;
}

interface Country {
	id: string;
	name: string;
	flag: string;
	rankings: RankingItem[];
}

const RankingDemo: React.FC = () => {
	const [selectedCountry, setSelectedCountry] = useState<string>('turkey');

	const countries: Country[] = [
		{
			id: 'turkey',
			name: 'Turkey',
			flag: '🇹🇷',
			rankings: [
				{
					rank: 1,
					name: 'Hagia Sophia',
					score: '9.8',
					type: 'Historical',
					distance: '2.1km',
				},
				{
					rank: 2,
					name: 'Blue Mosque',
					score: '9.7',
					type: 'Religious',
					distance: '2.3km',
				},
				{
					rank: 3,
					name: 'Grand Bazaar',
					score: '9.5',
					type: 'Shopping',
					distance: '1.8km',
				},
				{
					rank: 4,
					name: 'Topkapi Palace',
					score: '9.4',
					type: 'Historical',
					distance: '2.7km',
				},
				{
					rank: 5,
					name: 'Galata Tower',
					score: '9.2',
					type: 'Landmark',
					distance: '3.2km',
				},
			],
		},
		{
			id: 'france',
			name: 'France',
			flag: '🇫🇷',
			rankings: [
				{
					rank: 1,
					name: 'Eiffel Tower',
					score: '9.9',
					type: 'Landmark',
					distance: '1.5km',
				},
				{
					rank: 2,
					name: 'Louvre Museum',
					score: '9.8',
					type: 'Museum',
					distance: '2.1km',
				},
				{
					rank: 3,
					name: 'Notre-Dame',
					score: '9.6',
					type: 'Religious',
					distance: '1.9km',
				},
				{
					rank: 4,
					name: 'Arc de Triomphe',
					score: '9.4',
					type: 'Monument',
					distance: '2.8km',
				},
				{
					rank: 5,
					name: 'Sacré-Cœur',
					score: '9.3',
					type: 'Religious',
					distance: '3.5km',
				},
			],
		},
		{
			id: 'japan',
			name: 'Japan',
			flag: '🇯🇵',
			rankings: [
				{
					rank: 1,
					name: 'Fushimi Inari',
					score: '9.7',
					type: 'Temple',
					distance: '1.2km',
				},
				{
					rank: 2,
					name: 'Tokyo Skytree',
					score: '9.6',
					type: 'Landmark',
					distance: '2.4km',
				},
				{
					rank: 3,
					name: 'Senso-ji Temple',
					score: '9.5',
					type: 'Temple',
					distance: '1.8km',
				},
				{
					rank: 4,
					name: 'Mount Fuji View',
					score: '9.4',
					type: 'Nature',
					distance: '45km',
				},
				{
					rank: 5,
					name: 'Shibuya Crossing',
					score: '9.2',
					type: 'Urban',
					distance: '3.1km',
				},
			],
		},
	];

	const selectedCountryData = countries.find(
		(country) => country.id === selectedCountry
	);

	const handleCountryClick = (countryId: string) => {
		setSelectedCountry(countryId);
	};

	return (
		<div
			className='p-6 md:p-8 border backdrop-blur-sm'
			style={{
				background: `linear-gradient(135deg, ${colors.supporting['sup-color1']}10 0%, ${colors.neutral.cloudWhite}90 100%)`,
				borderColor: colors.supporting['sup-color1'],
				boxShadow: `0 12px 40px ${colors.supporting['sup-color1']}20`,
			}}>
			<div className='text-center mb-6'>
				<h3
					className='text-xl md:text-2xl font-bold mb-2'
					style={{ color: colors.supporting['sup-color1'] }}>
					📊 Live Ranking Demo
				</h3>
				<p
					className='text-sm md:text-base'
					style={{ color: colors.neutral.slateGray }}>
					Click on any country to see live rankings with interactive globe
				</p>
			</div>

			{/* Interactive Country Selection */}
			<div className='flex justify-center gap-4 mb-6'>
				{countries.map((country) => (
					<button
						key={country.id}
						onClick={() => handleCountryClick(country.id)}
						className={`px-4 py-2 rounded-lg transition-all duration-200 transform hover:scale-105 ${
							selectedCountry === country.id
								? 'bg-navy-500 text-white shadow-lg scale-105'
								: 'bg-white text-gray-700 hover:shadow-md'
						}`}
						style={{
							backgroundColor:
								selectedCountry === country.id
									? colors.brand.secondary
									: 'white',
							color:
								selectedCountry === country.id
									? 'white'
									: colors.neutral.slateGray,
						}}>
						<span className='mr-2'>{country.flag}</span>
						{country.name}
					</button>
				))}
			</div>

			{/* Live Ranking Interface */}
			<div className='bg-white rounded-lg p-4 shadow-inner max-w-4xl mx-auto'>
				<div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
					{/* Rankings List */}
					<div>
						<h4 className='font-bold mb-3 text-center'>
							{selectedCountryData?.flag} {selectedCountryData?.name} Top 5
						</h4>
						<div className='space-y-2'>
							{selectedCountryData?.rankings.map((item) => (
								<div
									key={item.rank}
									className='flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer'>
									<div className='flex items-center gap-3'>
										<span className='font-bold text-yellow-500 text-lg'>
											#{item.rank}
										</span>
										<div>
											<div className='font-medium text-sm'>{item.name}</div>
											<div className='text-xs text-gray-500'>
												{item.type} • {item.distance}
											</div>
										</div>
									</div>
									<div className='text-right'>
										<div className='font-bold text-sm'>{item.score}</div>
										<div className='text-xs text-gray-500'>score</div>
									</div>
								</div>
							))}
						</div>
					</div>

					{/* Interactive Globe Simulation */}
					<div className='flex items-center justify-center bg-gradient-to-br from-blue-50 to-green-50 rounded-lg p-6'>
						<div className='text-center'>
							<div className='text-6xl mb-4 animate-spin-slow'>🌍</div>
							<div
								className='text-lg font-bold mb-2'
								style={{ color: colors.brand.navy }}>
								Interactive 3D Globe
							</div>
							<div className='text-sm text-gray-600 mb-4'>
								Click {selectedCountryData?.flag} {selectedCountryData?.name} to
								explore
							</div>
							<div className='space-y-2'>
								<div className='text-xs bg-blue-100 px-2 py-1 rounded'>
									🔍 Zoom & Pan
								</div>
								<div className='text-xs bg-green-100 px-2 py-1 rounded'>
									📍 Click Countries
								</div>
								<div className='text-xs bg-yellow-100 px-2 py-1 rounded'>
									📊 Live Rankings
								</div>
							</div>
						</div>
					</div>
				</div>

				{/* Action Buttons */}
				<div className='text-center mt-6 space-y-3'>
					<div className='text-sm text-gray-600'>
						🎯 This is a live preview! The full ranking page has an interactive
						3D globe.
					</div>
					<button
						onClick={() => (window.location.href = '/ranking')}
						className='px-6 py-3 text-white rounded-lg hover:opacity-90 transition-all transform hover:scale-105'
						style={{ backgroundColor: colors.supporting['sup-color1'] }}>
						🌍 Launch Full Interactive Globe →
					</button>
				</div>
			</div>
		</div>
	);
};

export default RankingDemo;
