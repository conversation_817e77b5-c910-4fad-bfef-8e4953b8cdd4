/** @format */

'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

// Redirect component for old /globe route
export default function GlobeRedirect() {
	const router = useRouter();

	useEffect(() => {
		// Redirect to new /ranking route
		router.replace('/ranking');
	}, [router]);

	// Show loading state while redirecting
	return (
		<div className='min-h-screen flex items-center justify-center'>
			<div className='text-center'>
				<div className='animate-spin h-8 w-8 border-2 border-blue-500 border-t-transparent rounded-full mx-auto mb-4'></div>
				<p className='text-gray-600'>Redirecting to Ranking...</p>
			</div>
		</div>
	);
}
