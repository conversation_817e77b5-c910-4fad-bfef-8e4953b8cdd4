/** @format */

import React, { useEffect, useRef, useState } from 'react';

interface AsyncLocationFilterProps {
	onFiltersChange: (filters: {
		city?: string;
		district?: string;
		neighborhood?: string;
	}) => void;
}

const fetchSuggestions = async (
	endpoint: string,
	params: Record<string, string>
) => {
	const url = new URL(endpoint, window.location.origin);
	Object.entries(params).forEach(([key, value]) => {
		if (value) url.searchParams.append(key, value);
	});
	const res = await fetch(url.toString());
	const data = await res.json();
	return data.cities || data.districts || data.neighborhoods || [];
};

const AsyncDropdown: React.FC<{
	label: string;
	value: string | null;
	onChange: (value: string | null) => void;
	fetchOptions: (input: string) => Promise<string[]>;
	disabled?: boolean;
	placeholder?: string;
}> = ({ label, value, onChange, fetchOptions, disabled, placeholder }) => {
	const [input, setInput] = useState(value || '');
	const [options, setOptions] = useState<string[]>([]);
	const [open, setOpen] = useState(false);
	const [loading, setLoading] = useState(false);
	const debounceRef = useRef<NodeJS.Timeout>();

	useEffect(() => {
		setInput(value || '');
	}, [value]);

	useEffect(() => {
		if (!open || disabled) return;
		setLoading(true);
		if (debounceRef.current) clearTimeout(debounceRef.current);
		debounceRef.current = setTimeout(async () => {
			const opts = await fetchOptions(input);
			setOptions(opts);
			setLoading(false);
		}, 250);
	}, [input, open, disabled]);

	return (
		<div className='mb-3'>
			<label className='block text-sm font-medium mb-1 text-gray-700'>
				{label}
			</label>
			<div className='relative'>
				<input
					type='text'
					value={input}
					disabled={disabled}
					onFocus={() => setOpen(true)}
					onBlur={() => setTimeout(() => setOpen(false), 150)}
					onChange={(e) => {
						setInput(e.target.value);
						onChange(null);
					}}
					placeholder={placeholder}
					className='w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 border-gray-300'
				/>
				{open && !disabled && (
					<div className='absolute z-10 w-full mt-1 bg-white border rounded-lg shadow-lg max-h-48 overflow-y-auto'>
						{loading ? (
							<div className='px-3 py-2 text-sm text-gray-500'>Loading...</div>
						) : options.length === 0 ? (
							<div className='px-3 py-2 text-sm text-gray-500'>
								No options found
							</div>
						) : (
							options.map((option) => (
								<div
									key={option}
									onMouseDown={() => {
										setInput(option);
										onChange(option);
										setOpen(false);
									}}
									className={`px-3 py-2 text-sm cursor-pointer hover:bg-gray-100 ${
										option === value ? 'bg-blue-100' : ''
									}`}>
									{option}
								</div>
							))
						)}
					</div>
				)}
			</div>
		</div>
	);
};

const AsyncLocationFilter: React.FC<AsyncLocationFilterProps> = ({
	onFiltersChange,
}) => {
	const [city, setCity] = useState<string | null>(null);
	const [district, setDistrict] = useState<string | null>(null);
	const [neighborhood, setNeighborhood] = useState<string | null>(null);

	// Reset child filters when parent changes
	useEffect(() => {
		setDistrict(null);
		setNeighborhood(null);
	}, [city]);
	useEffect(() => {
		setNeighborhood(null);
	}, [district]);

	useEffect(() => {
		onFiltersChange({
			city: city || undefined,
			district: district || undefined,
			neighborhood: neighborhood || undefined,
		});
	}, [city, district, neighborhood, onFiltersChange]);

	return (
		<div className='bg-white rounded-lg p-4 shadow mb-4'>
			<AsyncDropdown
				label='City'
				value={city}
				onChange={setCity}
				fetchOptions={(input) =>
					fetchSuggestions('/api/pois/suggestions', {
						field: 'city',
						query: input,
					})
				}
				placeholder='Type to search city...'
			/>
			<AsyncDropdown
				label='District'
				value={district}
				onChange={setDistrict}
				fetchOptions={(input) =>
					city
						? fetchSuggestions('/api/pois/suggestions', {
								field: 'district',
								city,
								query: input,
						  })
						: Promise.resolve([])
				}
				disabled={!city}
				placeholder='Type to search district...'
			/>
			<AsyncDropdown
				label='Neighborhood'
				value={neighborhood}
				onChange={setNeighborhood}
				fetchOptions={(input) =>
					city && district
						? fetchSuggestions('/api/pois/suggestions', {
								field: 'neighborhood',
								city,
								district,
								query: input,
						  })
						: Promise.resolve([])
				}
				disabled={!city || !district}
				placeholder='Type to search neighborhood...'
			/>
		</div>
	);
};

export default AsyncLocationFilter;
