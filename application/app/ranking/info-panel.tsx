/** @format */

'use client';

import { FaTimes } from 'react-icons/fa';

interface InfoPanelProps {
	showInfo: boolean;
	setShowInfo: (show: boolean) => void;
}

export default function InfoPanel({ showInfo, setShowInfo }: InfoPanelProps) {
	if (!showInfo) return null;

	return (
		<div className='absolute bottom-8 left-8 z-20 animate-slide-down'>
			<div className='bg-white/95 backdrop-blur-sm p-6 shadow-xl max-w-sm border border-gray-200'>
				<div className='flex items-center justify-between mb-4'>
					<h3 className='text-lg font-semibold text-slate-800'>
						Globe Controls
					</h3>
					<button
						onClick={() => setShowInfo(false)}
						className='text-slate-500 hover:text-slate-700 transition-colors'>
						<FaTimes className='w-4 h-4' />
					</button>
				</div>
				<div className='space-y-3 text-sm text-slate-600'>
					<div className='flex items-center space-x-3'>
						<span className='text-lg'>🖱️</span>
						<span>Drag to rotate the globe</span>
					</div>
					<div className='flex items-center space-x-3'>
						<span className='text-lg'>🔍</span>
						<span>Scroll to zoom in/out</span>
					</div>
					<div className='flex items-center space-x-3'>
						<span className='text-lg'>🗺️</span>
						<span>Auto-flattens to 2D map when zoomed close</span>
					</div>
					<div className='flex items-center space-x-3'>
						<span className='text-lg'>📍</span>
						<span>Red marker shows your location</span>
					</div>
					<div className='flex items-center space-x-3'>
						<span className='text-lg'>⌨️</span>
						<span>Press ESC to go back or return to globe</span>
					</div>
					<div className='flex items-center space-x-3'>
						<span className='text-lg'>⎵</span>
						<span>Press Space to return to globe from map</span>
					</div>
				</div>
			</div>
		</div>
	);
}
