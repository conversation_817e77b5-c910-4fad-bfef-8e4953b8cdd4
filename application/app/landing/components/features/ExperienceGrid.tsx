/** @format */

'use client';

import { colors } from '@/app/colors';
import React from 'react';
import { FiArrowRight } from 'react-icons/fi';

interface ExperienceGridProps {
	onCategorySelect?: (category: any) => void;
}

interface ExperienceCategory {
	id: string;
	emoji: string;
	title: string;
	description: string;
	color: string;
	gradient: string;
}

const ExperienceGrid: React.FC<ExperienceGridProps> = ({
	onCategorySelect,
}) => {
	const experienceCategories: ExperienceCategory[] = [
		{
			id: 'food-drink',
			emoji: '🍽️',
			title: 'Food & Drink',
			description: 'Restaurants, cafes, bars, and culinary experiences',
			color: colors.brand.blue,
			gradient: `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.supporting.lightBlue} 100%)`,
		},
		{
			id: 'cultural-experiences',
			emoji: '🎭',
			title: 'Cultural Experiences',
			description: 'Museums, theaters, galleries, and cultural sites',
			color: colors.brand.green,
			gradient: `linear-gradient(135deg, ${colors.brand.green} 0%, ${colors.supporting.mintGreen} 100%)`,
		},
		{
			id: 'wellness-beauty',
			emoji: '🧘',
			title: 'Wellness & Beauty',
			description: 'Spas, yoga studios, wellness centers, and beauty services',
			color: colors.supporting.purple,
			gradient: `linear-gradient(135deg, ${colors.supporting.purple} 0%, ${colors.brand.blue} 100%)`,
		},
		{
			id: 'sports-fitness',
			emoji: '⚽',
			title: 'Sports & Fitness',
			description:
				'Gyms, sports facilities, outdoor activities, and fitness centers',
			color: colors.brand.navy,
			gradient: `linear-gradient(135deg, ${colors.brand.navy} 0%, ${colors.supporting.darkBlue} 100%)`,
		},
		{
			id: 'markets-shopping',
			emoji: '🛍️',
			title: 'Markets & Shopping',
			description:
				'Shopping centers, markets, boutiques, and retail experiences',
			color: colors.supporting['sup-color1'],
			gradient: `linear-gradient(135deg, ${colors.supporting['sup-color1']} 0%, ${colors.supporting['sup-color2']} 100%)`,
		},
		{
			id: 'entertainment',
			emoji: '🎪',
			title: 'Entertainment',
			description:
				'Cinemas, amusement parks, nightlife, and entertainment venues',
			color: colors.supporting['sup-color5'],
			gradient: `linear-gradient(135deg, ${colors.supporting['sup-color5']} 0%, ${colors.supporting['sup-color6']} 100%)`,
		},
	];

	const handleCategoryClick = (category: ExperienceCategory) => {
		if (onCategorySelect) {
			onCategorySelect({
				id: category.id,
				name: category.title,
				emoji: category.emoji,
			});
		}
		// Navigate to explore page with category filter
		window.location.href = `/explore?category=${encodeURIComponent(
			category.title
		)}`;
	};

	return (
		<div className='w-full'>
			{/* Section Header */}
			<div className='text-center mb-8 md:mb-12'>
				<div className='flex justify-center mb-4 md:mb-6'>
					<div
						className='inline-flex items-center space-x-2 px-4 py-2 md:px-6 md:py-3 border backdrop-blur-sm rounded-full'
						style={{
							background: `linear-gradient(135deg, ${colors.ui.green50}80 0%, ${colors.ui.blue50}80 100%)`,
							borderColor: colors.ui.gray200,
							boxShadow: '0 4px 16px rgba(51, 194, 255, 0.08)',
						}}>
						<span className='text-lg'>🧭</span>
						<span
							className='text-sm md:text-base font-medium'
							style={{ color: colors.neutral.textBlack }}>
							Explore Categories
						</span>
					</div>
				</div>

				<h2
					className='text-2xl md:text-3xl lg:text-4xl font-bold mb-4 md:mb-6'
					style={{ color: colors.brand.navy }}>
					Explore by Experience
				</h2>

				<p
					className='text-base md:text-lg max-w-2xl mx-auto leading-relaxed'
					style={{ color: colors.neutral.slateGray }}>
					Discover amazing places organized by the experiences you love most.
				</p>
			</div>

			{/* Experience Categories Grid */}
			<div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6 lg:gap-8 mb-8 md:mb-12'>
				{experienceCategories.map((category, index) => (
					<div
						key={category.id}
						onClick={() => handleCategoryClick(category)}
						className='group relative p-6 md:p-8 border backdrop-blur-sm cursor-pointer transition-all duration-300 hover:scale-105 hover:shadow-2xl'
						style={{
							background: `linear-gradient(135deg, ${colors.neutral.cloudWhite}95 0%, ${colors.ui.blue50}30 100%)`,
							borderColor: colors.ui.gray200,
							boxShadow: '0 8px 32px rgba(51, 194, 255, 0.08)',
							animationDelay: `${index * 150}ms`,
							animation: 'fadeInUp 0.8s ease-out forwards',
						}}>
						{/* Emoji Icon */}
						<div className='text-center mb-4 md:mb-6'>
							<div
								className='w-16 h-16 md:w-20 md:h-20 flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300'
								style={{
									background: category.gradient,
									borderRadius: '20px',
								}}>
								<span className='text-2xl md:text-3xl'>{category.emoji}</span>
							</div>
						</div>

						{/* Content */}
						<div className='text-center'>
							<h3
								className='text-lg md:text-xl font-bold mb-2 md:mb-3'
								style={{ color: colors.brand.navy }}>
								{category.title}
							</h3>
							<p
								className='text-sm md:text-base leading-relaxed mb-4'
								style={{ color: colors.neutral.slateGray }}>
								{category.description}
							</p>

							{/* Explore Button */}
							<div className='flex items-center justify-center space-x-2 text-sm font-medium group-hover:scale-105 transition-transform duration-300'>
								<span style={{ color: category.color }}>Explore</span>
								<FiArrowRight
									className='w-4 h-4 group-hover:translate-x-1 transition-transform duration-300'
									style={{ color: category.color }}
								/>
							</div>
						</div>

						{/* Hover Effect Overlay */}
						<div
							className='absolute inset-0 opacity-0 group-hover:opacity-10 transition-opacity duration-300 rounded-lg'
							style={{
								background: category.gradient,
							}}
						/>
					</div>
				))}
			</div>

			{/* CTA Button */}
			<div className='text-center'>
				<button
					onClick={() => {
						window.location.href = '/explore';
					}}
					className='group inline-flex items-center space-x-3 px-6 py-3 md:px-8 md:py-4 font-bold text-base md:text-lg transition-all duration-200 transform hover:scale-105 active:scale-95'
					style={{
						background: colors.brand.primary,
						color: 'white',
						boxShadow: `0 4px 12px ${colors.brand.primary}40`,
						borderRadius: '12px',
					}}>
					<span>Explore All Categories</span>
					<FiArrowRight className='w-5 h-5 group-hover:translate-x-1 transition-transform duration-300' />
				</button>
			</div>
		</div>
	);
};

export default ExperienceGrid;
