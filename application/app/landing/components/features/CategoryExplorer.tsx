/** @format */

'use client';

import { useViewportHeight } from '@/app/landing/utils/responsiveUtils';
import React from 'react';
import ExperienceGrid from './ExperienceGrid';

interface CategoryExplorerProps {
	onCategorySelect?: (category: any) => void;
}

const CategoryExplorer: React.FC<CategoryExplorerProps> = ({
	onCategorySelect,
}) => {
	// Use viewport height for responsive section sizing
	const { minHeight, padding } = useViewportHeight('section');

	return (
		<div
			className='bg-transparent viewport-section'
			style={{
				minHeight,
				padding,
			}}>
			{/* Main Content Container */}
			<div className='w-full px-4 md:px-6 lg:px-12 max-w-7xl mx-auto'>
				<ExperienceGrid onCategorySelect={onCategorySelect} />
			</div>
		</div>
	);
};

export default CategoryExplorer;
