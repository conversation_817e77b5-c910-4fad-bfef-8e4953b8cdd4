✅ WIZLOP LANDING PAGE REDESIGN + CODEBASE REFACTOR TASKS
================================================================

📌 GOAL: Redesign the Wizlop landing page to improve UX, clarity, and engagement.
         Refactor the codebase for naming consistency and cleaner structure.

🔷 PART 1: LANDING PAGE REDESIGN
================================

🔶 SECTION 1 — HERO / PRIMARY CTA
[ ] 1.1 Update hero section main heading to "Wizlop"
[ ] 1.2 Update subheading to "Explore Life. Experience Everything."
[ ] 1.3 Replace current CTA with three large buttons:
    - 💬 Chat
    - 🧭 Explore (was POIs)  
    - 📊 Ranking (was Globe)
[ ] 1.4 Add demo/image placeholders below each button:
    - Chat: AI query → smart location answers
    - Explore: Grid/list of categories (Food, Beauty, etc.)
    - Ranking: Top 10 places nearby (leaderboard style)
[ ] 1.5 Add tagline: "Chat. Discover. Rank. All in one place."

🔶 SECTION 2 — TECHNOLOGY SUMMARY
[ ] 2.1 Create new section with title "Smart tech that finds great places — fast."
[ ] 2.2 Add 3-column/grid layout with:
    - ⚡ Instant search powered by spatial tech
    - 📍 Location-based filtering with smart results
    - 🧠 AI understands what you're looking for
[ ] 2.3 Ensure light, benefit-driven tone (no technical jargon)

🔶 SECTION 3 — EXPLORE CATEGORIES
[ ] 3.1 Replace old category layout with visual grid
[ ] 3.2 Add title "Explore by Experience"
[ ] 3.3 Create tiles with emoji + label:
    - 🍽️ Food & Drink
    - 🎭 Cultural Experiences
    - 🧘 Wellness & Beauty
    - ⚽ Sports & Fitness
    - 🛍️ Markets & Shopping
    - ➕ Add more as needed
[ ] 3.4 Add CTA button: "Explore All Categories →"

🔶 SECTION 4 — ADVANCED TECH (COLLAPSIBLE)
[ ] 4.1 Make "Advanced Technology" section collapsible by default
[ ] 4.2 Keep only 3 short summaries:
    - PostGIS-powered search
    - Context-aware AI queries
    - Smart ranking with distance + preferences
[ ] 4.3 Remove over-detailed PostGIS/algorithms explanation

🔶 SECTION 5 — FOOTER CLEANUP
[ ] 5.1 Keep existing footer structure
[ ] 5.2 Ensure clean mission statement: "Revolutionizing location discovery through conversational AI"
[ ] 5.3 Keep "Made with ❤️ in Istanbul"
[ ] 5.4 Maintain quick links: About Us, Contact, Privacy
[ ] 5.5 Optionally add social icons & newsletter field

🔷 PART 2: COMPONENT + FILE RENAME (GLOBE → RANKING, POIs → EXPLORE)
===================================================================

🔁 RENAME ALL OCCURRENCES:
[ ] 6.1 Rename globe → ranking in all files:
    - GlobePage.tsx → RankingPage.tsx
    - globe_view, GlobeComponent → ranking_view, RankingComponent
    - globe.css → ranking.css
    - Routes /globe → /ranking

[ ] 6.2 Rename POIs → Explore in all files:
    - POIsPage.tsx → ExplorePage.tsx
    - POIComponent, POIContainer → ExploreComponent, ExploreContainer
    - Routes /pois → /explore

[ ] 6.3 Update component names in:
    - File and folder names
    - Class names
    - Variable names
    - Function names

[ ] 6.4 Update UI labels and text:
    - Button labels
    - Navigation text
    - Page titles
    - Breadcrumbs
    - SEO metadata

🔷 PART 3: ROUTING UPDATES
==========================
[ ] 7.1 Update route definitions:
    - /globe → /ranking
    - /pois → /explore
[ ] 7.2 Update navigation-config.ts
[ ] 7.3 Update internal navigation logic
[ ] 7.4 Update deep links and breadcrumbs
[ ] 7.5 Add redirects for old routes or fallback pages

🔷 PART 4: REMOVE REDUNDANT CONTENT
===================================
[ ] 8.1 Remove "AI Assistant" section with input box
[ ] 8.2 Remove duplicate/verbose "Explore Categories" gradient section
[ ] 8.3 Remove overly technical paragraphs (unless moved to collapsible)
[ ] 8.4 Clean up unused components

🔷 PART 5: FINAL REVIEW CHECKLIST
=================================
[ ] 9.1 Verify landing page has new layout and CTA structure
[ ] 9.2 Confirm all "globe" and "POIs" references are renamed
[ ] 9.3 Add new demos/previews or placeholder visuals
[ ] 9.4 Test mobile responsiveness
[ ] 9.5 Verify internal routes & navigation work
[ ] 9.6 Check for broken links or 404s
[ ] 9.7 Ensure footer and tech summary sections are clean
[ ] 9.8 Clean up code and remove unused components
[ ] 9.9 Test all functionality works after changes
[ ] 9.10 Commit changes with clear message

🧰 TECHNICAL NOTES:
==================
- Stack: React/Next.js with TypeScript
- Update dynamic routing and clean imports after renaming
- Update pages/, components/, styles/ folders
- Ensure no mixed usage (e.g., globe in variable but ranking in UI)
- Maintain existing functionality while improving design
- Follow existing code patterns and styling conventions
